// ===== Field Selector Content Script =====
// يعمل داخل الصفحات المستهدفة لتحديد الحقول وربطها

class FieldSelector {
    constructor() {
        this.isActive = false;
        this.selectedFields = {};
        this.columns = [];
        this.sampleData = {};
        this.overlay = null;
        this.highlightedElement = null;
        
        this.setupMessageListener();
        console.log('🎯 Field Selector مُحمل ومستعد');
    }

    setupMessageListener() {
        window.addEventListener('message', (event) => {
            if (event.data && event.data.action) {
                this.handleMessage(event.data);
            }
        });
    }

    handleMessage(data) {
        switch (data.action) {
            case 'activateFieldSelection':
                this.activate(data.columns, data.sampleData);
                break;
            case 'deactivateFieldSelection':
                this.deactivate();
                break;
            case 'confirmFieldMapping':
                this.confirmFieldMapping(data.mapping);
                break;
            case 'cancelFieldMapping':
                this.cancelFieldMapping();
                break;
        }
    }

    activate(columns, sampleData) {
        console.log('🎯 تفعيل وضع تحديد الحقول');
        
        this.isActive = true;
        this.columns = columns;
        this.sampleData = sampleData;
        
        this.createOverlay();
        this.attachEventListeners();
        this.showInstructions();
    }

    deactivate() {
        console.log('⏹️ إيقاف وضع تحديد الحقول');
        
        this.isActive = false;
        this.removeOverlay();
        this.removeEventListeners();
        this.clearHighlight();
    }

    createOverlay() {
        // إنشاء طبقة شفافة لعرض التعليمات
        this.overlay = document.createElement('div');
        this.overlay.id = 'field-selector-overlay';
        this.overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 123, 255, 0.1);
            z-index: 9999;
            pointer-events: none;
            direction: rtl;
            font-family: Arial, sans-serif;
        `;

        document.body.appendChild(this.overlay);
    }

    removeOverlay() {
        if (this.overlay) {
            this.overlay.remove();
            this.overlay = null;
        }
    }

    showInstructions() {
        const instructions = document.createElement('div');
        instructions.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10001;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            animation: fadeInDown 0.5s ease;
            direction: rtl;
        `;

        instructions.innerHTML = `
            🎯 انقر على الحقول لربطها بالبيانات
            <div style="font-size: 12px; margin-top: 5px; opacity: 0.9;">
                ${this.columns.length} عمود متاح للربط
            </div>
        `;

        // إضافة الأنيميشن
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInDown {
                from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
                to { opacity: 1; transform: translateX(-50%) translateY(0); }
            }
        `;
        document.head.appendChild(style);

        this.overlay.appendChild(instructions);

        // إزالة التعليمات بعد 5 ثوان
        setTimeout(() => {
            if (instructions.parentNode) {
                instructions.style.animation = 'fadeInDown 0.5s ease reverse';
                setTimeout(() => instructions.remove(), 500);
            }
        }, 5000);
    }

    attachEventListeners() {
        this.clickHandler = (e) => this.handleFieldClick(e);
        this.hoverHandler = (e) => this.handleFieldHover(e);
        this.leaveHandler = (e) => this.handleFieldLeave(e);

        document.addEventListener('click', this.clickHandler, true);
        document.addEventListener('mouseover', this.hoverHandler, true);
        document.addEventListener('mouseout', this.leaveHandler, true);
    }

    removeEventListeners() {
        if (this.clickHandler) {
            document.removeEventListener('click', this.clickHandler, true);
            document.removeEventListener('mouseover', this.hoverHandler, true);
            document.removeEventListener('mouseout', this.leaveHandler, true);
        }
    }

    handleFieldClick(e) {
        if (!this.isActive) return;

        const element = e.target;
        
        // التحقق من أن العنصر حقل قابل للتعبئة
        if (!this.isFormField(element)) return;

        e.preventDefault();
        e.stopPropagation();

        console.log('🎯 تم النقر على حقل:', element);

        const field = this.extractFieldInfo(element);
        this.sendFieldSelected(field);
    }

    handleFieldHover(e) {
        if (!this.isActive) return;

        const element = e.target;
        
        if (this.isFormField(element)) {
            this.highlightElement(element);
        }
    }

    handleFieldLeave(e) {
        if (!this.isActive) return;
        this.clearHighlight();
    }

    isFormField(element) {
        const tagName = element.tagName.toLowerCase();
        const type = element.type ? element.type.toLowerCase() : '';

        const validTags = ['input', 'textarea', 'select'];
        const validTypes = ['text', 'email', 'number', 'tel', 'url', 'password', 'search'];

        if (tagName === 'input') {
            return validTypes.includes(type) || type === '';
        }

        return validTags.includes(tagName);
    }

    extractFieldInfo(element) {
        const field = {
            selector: this.generateElementSelector(element),
            type: element.type || element.tagName.toLowerCase(),
            name: this.getFieldName(element),
            placeholder: element.placeholder || '',
            value: element.value || '',
            required: element.required || false
        };

        // إضافة خيارات القائمة المنسدلة
        if (element.tagName.toLowerCase() === 'select') {
            field.options = Array.from(element.options).map(option => ({
                value: option.value,
                text: option.textContent.trim()
            }));
        }

        return field;
    }

    getFieldName(element) {
        // محاولة الحصول على اسم الحقل من مصادر مختلفة
        if (element.name) return element.name;
        if (element.id) return element.id;
        if (element.placeholder) return element.placeholder;
        
        // البحث في التسميات المرتبطة
        const label = this.findAssociatedLabel(element);
        if (label) return label.textContent.trim();

        // البحث في النص المجاور
        const nearbyText = this.findNearbyText(element);
        if (nearbyText) return nearbyText;

        return `حقل ${element.tagName.toLowerCase()}`;
    }

    findAssociatedLabel(element) {
        // البحث عن label مرتبط بـ for
        if (element.id) {
            const label = document.querySelector(`label[for="${element.id}"]`);
            if (label) return label;
        }

        // البحث عن label يحتوي على العنصر
        let parent = element.parentNode;
        while (parent && parent.tagName) {
            if (parent.tagName.toLowerCase() === 'label') {
                return parent;
            }
            parent = parent.parentNode;
        }

        return null;
    }

    findNearbyText(element) {
        // البحث في العناصر المجاورة
        const siblings = Array.from(element.parentNode.children);
        const elementIndex = siblings.indexOf(element);

        // البحث في العنصر السابق
        for (let i = elementIndex - 1; i >= 0; i--) {
            const sibling = siblings[i];
            const text = sibling.textContent.trim();
            if (text && text.length < 50) {
                return text;
            }
        }

        return null;
    }

    generateElementSelector(element) {
        if (element.id) {
            return `#${element.id}`;
        }
        
        if (element.name) {
            return `[name="${element.name}"]`;
        }
        
        // إنشاء محدد CSS فريد
        let selector = element.tagName.toLowerCase();
        
        if (element.className) {
            const classes = element.className.split(' ').filter(cls => cls.trim());
            if (classes.length > 0) {
                selector += '.' + classes.join('.');
            }
        }
        
        // إضافة placeholder كمحدد إضافي
        if (element.placeholder) {
            selector += `[placeholder="${element.placeholder}"]`;
        }
        
        return selector;
    }

    highlightElement(element) {
        this.clearHighlight();
        
        this.highlightedElement = element;
        element.style.outline = '3px solid #007bff';
        element.style.outlineOffset = '2px';
        element.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
        
        // إضافة tooltip
        this.showTooltip(element);
    }

    clearHighlight() {
        if (this.highlightedElement) {
            this.highlightedElement.style.outline = '';
            this.highlightedElement.style.outlineOffset = '';
            this.highlightedElement.style.backgroundColor = '';
            this.highlightedElement = null;
        }
        
        this.hideTooltip();
    }

    showTooltip(element) {
        const tooltip = document.createElement('div');
        tooltip.id = 'field-selector-tooltip';
        tooltip.style.cssText = `
            position: absolute;
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 10002;
            pointer-events: none;
            direction: rtl;
        `;
        
        const fieldName = this.getFieldName(element);
        tooltip.textContent = `انقر لربط: ${fieldName}`;
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + 'px';
        tooltip.style.top = (rect.top - 35) + 'px';
        
        document.body.appendChild(tooltip);
    }

    hideTooltip() {
        const tooltip = document.getElementById('field-selector-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    sendFieldSelected(field) {
        window.parent.postMessage({
            source: 'field-selector',
            action: 'fieldSelected',
            field: field
        }, '*');
    }

    confirmFieldMapping(mapping) {
        console.log('✅ تأكيد ربط الحقل:', mapping);
        
        // حفظ الربط محلياً
        this.selectedFields[mapping.selector] = mapping;
        
        // إضافة علامة على الحقل المربوط
        const element = document.querySelector(mapping.selector);
        if (element) {
            this.markFieldAsMapped(element, mapping);
        }

        // إرسال تأكيد الربط
        window.parent.postMessage({
            source: 'field-selector',
            action: 'fieldMapped',
            mapping: mapping
        }, '*');
    }

    markFieldAsMapped(element, mapping) {
        element.style.border = '2px solid #28a745';
        element.style.backgroundColor = 'rgba(40, 167, 69, 0.1)';
        
        // إضافة أيقونة تأكيد
        const icon = document.createElement('span');
        icon.style.cssText = `
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 1000;
        `;
        icon.textContent = '✓';
        
        // جعل العنصر الأب relative إذا لم يكن كذلك
        const parent = element.parentNode;
        if (getComputedStyle(parent).position === 'static') {
            parent.style.position = 'relative';
        }
        
        parent.appendChild(icon);
    }

    cancelFieldMapping() {
        console.log('❌ إلغاء ربط الحقل');
        this.clearHighlight();
    }
}

// تشغيل Field Selector عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new FieldSelector();
    });
} else {
    new FieldSelector();
}
