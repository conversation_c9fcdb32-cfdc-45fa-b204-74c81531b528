// ===== Field Selector Content Script =====
// يعمل داخل الصفحات المستهدفة لتحديد الحقول وربطها

class FieldSelector {
    constructor() {
        this.isActive = false;
        this.selectedFields = {};
        this.columns = [];
        this.sampleData = {};
        this.overlay = null;
        this.highlightedElement = null;
        
        this.setupMessageListener();
        console.log('🎯 Field Selector مُحمل ومستعد');
    }

    setupMessageListener() {
        window.addEventListener('message', (event) => {
            if (event.data && event.data.action) {
                this.handleMessage(event.data);
            }
        });
    }

    handleMessage(data) {
        switch (data.action) {
            case 'activateFieldSelection':
                this.activate(data.columns, data.sampleData);
                break;
            case 'deactivateFieldSelection':
                this.deactivate();
                break;
            case 'confirmFieldMapping':
                this.confirmFieldMapping(data.mapping);
                break;
            case 'cancelFieldMapping':
                this.cancelFieldMapping();
                break;
        }
    }

    activate(columns, sampleData) {
        console.log('🎯 تفعيل وضع تحديد الحقول');
        
        this.isActive = true;
        this.columns = columns;
        this.sampleData = sampleData;
        
        this.createOverlay();
        this.attachEventListeners();
        this.showInstructions();
    }

    deactivate() {
        console.log('⏹️ إيقاف وضع تحديد الحقول');
        
        this.isActive = false;
        this.removeOverlay();
        this.removeEventListeners();
        this.clearHighlight();
    }

    createOverlay() {
        // إنشاء طبقة شفافة لعرض التعليمات
        this.overlay = document.createElement('div');
        this.overlay.id = 'field-selector-overlay';
        this.overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 123, 255, 0.1);
            z-index: 9999;
            pointer-events: none;
            direction: rtl;
            font-family: Arial, sans-serif;
        `;

        document.body.appendChild(this.overlay);
    }

    removeOverlay() {
        if (this.overlay) {
            this.overlay.remove();
            this.overlay = null;
        }
    }

    showInstructions() {
        const instructions = document.createElement('div');
        instructions.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10001;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            animation: fadeInDown 0.5s ease;
            direction: rtl;
        `;

        instructions.innerHTML = `
            🖱️ انقر بالزر الأيمن على الحقول لربطها
            <div style="font-size: 12px; margin-top: 5px; opacity: 0.9;">
                ${this.columns.length} عمود متاح للربط • النقر الأيمن = قائمة الأعمدة
            </div>
        `;

        // إضافة الأنيميشن
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInDown {
                from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
                to { opacity: 1; transform: translateX(-50%) translateY(0); }
            }
        `;
        document.head.appendChild(style);

        this.overlay.appendChild(instructions);

        // إزالة التعليمات بعد 5 ثوان
        setTimeout(() => {
            if (instructions.parentNode) {
                instructions.style.animation = 'fadeInDown 0.5s ease reverse';
                setTimeout(() => instructions.remove(), 500);
            }
        }, 5000);
    }

    attachEventListeners() {
        this.clickHandler = (e) => this.handleFieldClick(e);
        this.hoverHandler = (e) => this.handleFieldHover(e);
        this.leaveHandler = (e) => this.handleFieldLeave(e);
        this.contextMenuHandler = (e) => this.handleRightClick(e);

        document.addEventListener('click', this.clickHandler, true);
        document.addEventListener('mouseover', this.hoverHandler, true);
        document.addEventListener('mouseout', this.leaveHandler, true);
        document.addEventListener('contextmenu', this.contextMenuHandler, true);
    }

    removeEventListeners() {
        if (this.clickHandler) {
            document.removeEventListener('click', this.clickHandler, true);
            document.removeEventListener('mouseover', this.hoverHandler, true);
            document.removeEventListener('mouseout', this.leaveHandler, true);
            document.removeEventListener('contextmenu', this.contextMenuHandler, true);
        }
    }

    handleFieldClick(e) {
        if (!this.isActive) return;

        const element = e.target;
        
        // التحقق من أن العنصر حقل قابل للتعبئة
        if (!this.isFormField(element)) return;

        e.preventDefault();
        e.stopPropagation();

        console.log('🎯 تم النقر على حقل:', element);

        const field = this.extractFieldInfo(element);
        this.sendFieldSelected(field);
    }

    handleFieldHover(e) {
        if (!this.isActive) return;

        const element = e.target;
        
        if (this.isFormField(element)) {
            this.highlightElement(element);
        }
    }

    handleFieldLeave(e) {
        if (!this.isActive) return;
        this.clearHighlight();
    }

    handleRightClick(e) {
        if (!this.isActive) return;

        const element = e.target;

        // التحقق من أن العنصر حقل قابل للتعبئة
        if (!this.isFormField(element)) return;

        e.preventDefault();
        e.stopPropagation();

        console.log('🖱️ نقر أيمن على حقل:', element);

        // إخفاء أي قائمة موجودة
        this.hideContextMenu();

        // إنشاء وإظهار قائمة الأعمدة
        this.showColumnContextMenu(e, element);
    }

    showColumnContextMenu(event, element) {
        const field = this.extractFieldInfo(element);

        // إنشاء قائمة السياق
        const contextMenu = document.createElement('div');
        contextMenu.id = 'column-context-menu';
        contextMenu.style.cssText = `
            position: fixed;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10003;
            min-width: 250px;
            max-height: 400px;
            overflow-y: auto;
            direction: rtl;
            font-family: Arial, sans-serif;
        `;

        // رأس القائمة
        const header = document.createElement('div');
        header.style.cssText = `
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 12px 15px;
            font-weight: bold;
            font-size: 14px;
            border-radius: 6px 6px 0 0;
        `;
        header.innerHTML = `🎯 ربط الحقل: ${field.name}`;

        contextMenu.appendChild(header);

        // قائمة الأعمدة
        const columnsList = document.createElement('div');
        columnsList.style.cssText = `
            padding: 8px 0;
        `;

        // إضافة خيار "بدون ربط"
        const noMappingOption = this.createColumnOption('', 'بدون ربط', '❌', element, field);
        columnsList.appendChild(noMappingOption);

        // إضافة خط فاصل
        const separator = document.createElement('div');
        separator.style.cssText = `
            height: 1px;
            background: #dee2e6;
            margin: 5px 0;
        `;
        columnsList.appendChild(separator);

        // إضافة الأعمدة
        this.columns.forEach(column => {
            const sampleData = this.getSampleDataForColumn(column);
            const option = this.createColumnOption(column, column, '📊', element, field, sampleData);
            columnsList.appendChild(option);
        });

        // إضافة خط فاصل
        const separator2 = document.createElement('div');
        separator2.style.cssText = `
            height: 1px;
            background: #dee2e6;
            margin: 5px 0;
        `;
        columnsList.appendChild(separator2);

        // إضافة خيار "قيمة مخصصة"
        const customOption = this.createCustomValueOption(element, field);
        columnsList.appendChild(customOption);

        contextMenu.appendChild(columnsList);

        // تحديد موقع القائمة
        const x = event.clientX;
        const y = event.clientY;

        contextMenu.style.left = x + 'px';
        contextMenu.style.top = y + 'px';

        document.body.appendChild(contextMenu);

        // إضافة مستمع لإغلاق القائمة عند النقر خارجها
        setTimeout(() => {
            document.addEventListener('click', this.hideContextMenuHandler = () => {
                this.hideContextMenu();
            });
        }, 100);
    }

    createColumnOption(columnValue, displayText, icon, element, field, sampleData = null) {
        const option = document.createElement('div');
        option.style.cssText = `
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 10px;
        `;

        option.innerHTML = `
            <span style="font-size: 16px;">${icon}</span>
            <div style="flex: 1;">
                <div style="font-weight: bold; color: #333;">${displayText}</div>
                ${sampleData ? `<div style="font-size: 12px; color: #666; margin-top: 2px;">مثال: ${sampleData}</div>` : ''}
            </div>
        `;

        // تأثيرات التحويم
        option.addEventListener('mouseenter', () => {
            option.style.backgroundColor = '#e3f2fd';
        });

        option.addEventListener('mouseleave', () => {
            option.style.backgroundColor = '';
        });

        // معالج النقر
        option.addEventListener('click', () => {
            this.selectColumn(columnValue, element, field);
        });

        return option;
    }

    createCustomValueOption(element, field) {
        const option = document.createElement('div');
        option.style.cssText = `
            padding: 10px 15px;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 10px;
        `;

        option.innerHTML = `
            <span style="font-size: 16px;">✏️</span>
            <div style="flex: 1;">
                <div style="font-weight: bold; color: #333;">قيمة مخصصة</div>
                <div style="font-size: 12px; color: #666; margin-top: 2px;">أدخل قيمة ثابتة</div>
            </div>
        `;

        // تأثيرات التحويم
        option.addEventListener('mouseenter', () => {
            option.style.backgroundColor = '#fff3cd';
        });

        option.addEventListener('mouseleave', () => {
            option.style.backgroundColor = '';
        });

        // معالج النقر
        option.addEventListener('click', () => {
            this.showCustomValueInput(element, field);
        });

        return option;
    }

    selectColumn(columnValue, element, field) {
        console.log('📊 تم اختيار العمود:', columnValue, 'للحقل:', field.name);

        this.hideContextMenu();

        if (!columnValue) {
            // إزالة الربط
            this.removeFieldMapping(element, field);
            return;
        }

        // إنشاء الربط
        const mapping = {
            selector: field.selector,
            column: columnValue,
            customValue: null,
            type: field.type,
            fieldName: field.name
        };

        // تطبيق الربط
        this.confirmFieldMapping(mapping);
    }

    showCustomValueInput(element, field) {
        this.hideContextMenu();

        // إنشاء نافذة إدخال القيمة المخصصة
        const inputDialog = document.createElement('div');
        inputDialog.id = 'custom-value-dialog';
        inputDialog.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #ffc107;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            z-index: 10004;
            min-width: 300px;
            direction: rtl;
            font-family: Arial, sans-serif;
        `;

        inputDialog.innerHTML = `
            <div style="background: linear-gradient(135deg, #ffc107, #e0a800); color: #212529; padding: 15px; border-radius: 10px 10px 0 0;">
                <h3 style="margin: 0; font-size: 16px;">✏️ قيمة مخصصة</h3>
                <div style="font-size: 14px; margin-top: 5px; opacity: 0.8;">${field.name}</div>
            </div>

            <div style="padding: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">أدخل القيمة:</label>
                <input type="text" id="custom-value-input" placeholder="القيمة المطلوبة..." style="width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px; direction: rtl;">

                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 15px;">
                    <button id="confirm-custom-value" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px;">✅ تأكيد</button>
                    <button id="cancel-custom-value" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px;">❌ إلغاء</button>
                </div>
            </div>
        `;

        document.body.appendChild(inputDialog);

        // التركيز على حقل الإدخال
        const input = inputDialog.querySelector('#custom-value-input');
        input.focus();

        // معالجات الأحداث
        inputDialog.querySelector('#confirm-custom-value').addEventListener('click', () => {
            const customValue = input.value.trim();
            if (!customValue) {
                alert('يرجى إدخال قيمة');
                return;
            }

            const mapping = {
                selector: field.selector,
                column: null,
                customValue: customValue,
                type: field.type,
                fieldName: field.name
            };

            this.confirmFieldMapping(mapping);
            inputDialog.remove();
        });

        inputDialog.querySelector('#cancel-custom-value').addEventListener('click', () => {
            inputDialog.remove();
        });

        // إدخال Enter للتأكيد
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                inputDialog.querySelector('#confirm-custom-value').click();
            }
        });
    }

    getSampleDataForColumn(columnName) {
        if (!this.sampleData || !this.sampleData[columnName]) return null;

        const sampleValue = this.sampleData[columnName];
        if (!sampleValue) return null;

        // اقتطاع النص إذا كان طويلاً
        const maxLength = 25;
        if (sampleValue.length > maxLength) {
            return sampleValue.substring(0, maxLength) + '...';
        }

        return sampleValue;
    }

    hideContextMenu() {
        const contextMenu = document.getElementById('column-context-menu');
        if (contextMenu) {
            contextMenu.remove();
        }

        const customDialog = document.getElementById('custom-value-dialog');
        if (customDialog) {
            customDialog.remove();
        }

        // إزالة مستمع إغلاق القائمة
        if (this.hideContextMenuHandler) {
            document.removeEventListener('click', this.hideContextMenuHandler);
            this.hideContextMenuHandler = null;
        }
    }

    removeFieldMapping(element, field) {
        console.log('❌ إزالة ربط الحقل:', field.name);

        // إزالة التمييز
        element.style.border = '';
        element.style.backgroundColor = '';

        // إزالة أيقونة التأكيد
        const icon = element.parentNode.querySelector('span');
        if (icon && icon.textContent === '✓') {
            icon.remove();
        }

        // إزالة من الحقول المحددة
        delete this.selectedFields[field.selector];

        // إرسال رسالة الإزالة
        window.parent.postMessage({
            source: 'field-selector',
            action: 'fieldRemoved',
            field: field
        }, '*');
    }

    isFormField(element) {
        const tagName = element.tagName.toLowerCase();
        const type = element.type ? element.type.toLowerCase() : '';

        const validTags = ['input', 'textarea', 'select'];
        const validTypes = ['text', 'email', 'number', 'tel', 'url', 'password', 'search'];

        if (tagName === 'input') {
            return validTypes.includes(type) || type === '';
        }

        return validTags.includes(tagName);
    }

    extractFieldInfo(element) {
        const field = {
            selector: this.generateElementSelector(element),
            type: element.type || element.tagName.toLowerCase(),
            name: this.getFieldName(element),
            placeholder: element.placeholder || '',
            value: element.value || '',
            required: element.required || false
        };

        // إضافة خيارات القائمة المنسدلة
        if (element.tagName.toLowerCase() === 'select') {
            field.options = Array.from(element.options).map(option => ({
                value: option.value,
                text: option.textContent.trim()
            }));
        }

        return field;
    }

    getFieldName(element) {
        // محاولة الحصول على اسم الحقل من مصادر مختلفة
        if (element.name) return element.name;
        if (element.id) return element.id;
        if (element.placeholder) return element.placeholder;
        
        // البحث في التسميات المرتبطة
        const label = this.findAssociatedLabel(element);
        if (label) return label.textContent.trim();

        // البحث في النص المجاور
        const nearbyText = this.findNearbyText(element);
        if (nearbyText) return nearbyText;

        return `حقل ${element.tagName.toLowerCase()}`;
    }

    findAssociatedLabel(element) {
        // البحث عن label مرتبط بـ for
        if (element.id) {
            const label = document.querySelector(`label[for="${element.id}"]`);
            if (label) return label;
        }

        // البحث عن label يحتوي على العنصر
        let parent = element.parentNode;
        while (parent && parent.tagName) {
            if (parent.tagName.toLowerCase() === 'label') {
                return parent;
            }
            parent = parent.parentNode;
        }

        return null;
    }

    findNearbyText(element) {
        // البحث في العناصر المجاورة
        const siblings = Array.from(element.parentNode.children);
        const elementIndex = siblings.indexOf(element);

        // البحث في العنصر السابق
        for (let i = elementIndex - 1; i >= 0; i--) {
            const sibling = siblings[i];
            const text = sibling.textContent.trim();
            if (text && text.length < 50) {
                return text;
            }
        }

        return null;
    }

    generateElementSelector(element) {
        if (element.id) {
            return `#${element.id}`;
        }
        
        if (element.name) {
            return `[name="${element.name}"]`;
        }
        
        // إنشاء محدد CSS فريد
        let selector = element.tagName.toLowerCase();
        
        if (element.className) {
            const classes = element.className.split(' ').filter(cls => cls.trim());
            if (classes.length > 0) {
                selector += '.' + classes.join('.');
            }
        }
        
        // إضافة placeholder كمحدد إضافي
        if (element.placeholder) {
            selector += `[placeholder="${element.placeholder}"]`;
        }
        
        return selector;
    }

    highlightElement(element) {
        this.clearHighlight();
        
        this.highlightedElement = element;
        element.style.outline = '3px solid #007bff';
        element.style.outlineOffset = '2px';
        element.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
        
        // إضافة tooltip
        this.showTooltip(element);
    }

    clearHighlight() {
        if (this.highlightedElement) {
            this.highlightedElement.style.outline = '';
            this.highlightedElement.style.outlineOffset = '';
            this.highlightedElement.style.backgroundColor = '';
            this.highlightedElement = null;
        }
        
        this.hideTooltip();
    }

    showTooltip(element) {
        const tooltip = document.createElement('div');
        tooltip.id = 'field-selector-tooltip';
        tooltip.style.cssText = `
            position: absolute;
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 10002;
            pointer-events: none;
            direction: rtl;
        `;
        
        const fieldName = this.getFieldName(element);
        tooltip.textContent = `نقر أيمن لربط: ${fieldName}`;
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + 'px';
        tooltip.style.top = (rect.top - 35) + 'px';
        
        document.body.appendChild(tooltip);
    }

    hideTooltip() {
        const tooltip = document.getElementById('field-selector-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    sendFieldSelected(field) {
        window.parent.postMessage({
            source: 'field-selector',
            action: 'fieldSelected',
            field: field
        }, '*');
    }

    confirmFieldMapping(mapping) {
        console.log('✅ تأكيد ربط الحقل:', mapping);
        
        // حفظ الربط محلياً
        this.selectedFields[mapping.selector] = mapping;
        
        // إضافة علامة على الحقل المربوط
        const element = document.querySelector(mapping.selector);
        if (element) {
            this.markFieldAsMapped(element, mapping);
        }

        // إرسال تأكيد الربط
        window.parent.postMessage({
            source: 'field-selector',
            action: 'fieldMapped',
            mapping: mapping
        }, '*');
    }

    markFieldAsMapped(element, mapping) {
        element.style.border = '2px solid #28a745';
        element.style.backgroundColor = 'rgba(40, 167, 69, 0.1)';
        
        // إضافة أيقونة تأكيد
        const icon = document.createElement('span');
        icon.style.cssText = `
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 1000;
        `;
        icon.textContent = '✓';
        
        // جعل العنصر الأب relative إذا لم يكن كذلك
        const parent = element.parentNode;
        if (getComputedStyle(parent).position === 'static') {
            parent.style.position = 'relative';
        }
        
        parent.appendChild(icon);
    }

    cancelFieldMapping() {
        console.log('❌ إلغاء ربط الحقل');
        this.clearHighlight();
    }
}

// تشغيل Field Selector عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new FieldSelector();
    });
} else {
    new FieldSelector();
}
