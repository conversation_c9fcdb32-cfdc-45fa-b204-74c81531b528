<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الجديد - نموذج إنشاء منتج</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            direction: rtl;
        }
        
        .form-container {
            background: #f9f9f9;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        textarea {
            height: 100px;
            resize: vertical;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .submit-btn {
            background: #4facfe;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
        }
        
        .submit-btn:hover {
            background: #3d8bfe;
        }
        
        .required {
            color: red;
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>🛍️ إنشاء منتج جديد</h1>
        
        <div class="info-box">
            <strong>تعليمات:</strong> هذا نموذج اختبار للنظام الجديد. استخدم الإضافة لتحليل هذه الصفحة وربط الحقول بالبيانات.
        </div>
        
        <form id="product-form">
            <div class="form-group">
                <label for="product-name">اسم المنتج <span class="required">*</span></label>
                <input type="text" id="product-name" name="product_name" placeholder="أدخل اسم المنتج" required>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="product-price">السعر <span class="required">*</span></label>
                    <input type="number" id="product-price" name="price" placeholder="0.00" step="0.01" required>
                </div>
                
                <div class="form-group">
                    <label for="product-category">الفئة</label>
                    <select id="product-category" name="category">
                        <option value="">-- اختر الفئة --</option>
                        <option value="electronics">إلكترونيات</option>
                        <option value="clothing">ملابس</option>
                        <option value="books">كتب</option>
                        <option value="home">منزل وحديقة</option>
                        <option value="sports">رياضة</option>
                        <option value="beauty">جمال وعناية</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="product-description">وصف المنتج</label>
                <textarea id="product-description" name="description" placeholder="أدخل وصف مفصل للمنتج"></textarea>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="product-brand">العلامة التجارية</label>
                    <input type="text" id="product-brand" name="brand" placeholder="اسم العلامة التجارية">
                </div>
                
                <div class="form-group">
                    <label for="product-sku">رمز المنتج (SKU)</label>
                    <input type="text" id="product-sku" name="sku" placeholder="SKU-001">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="product-weight">الوزن (كجم)</label>
                    <input type="number" id="product-weight" name="weight" placeholder="0.0" step="0.1">
                </div>
                
                <div class="form-group">
                    <label for="product-stock">الكمية المتاحة</label>
                    <input type="number" id="product-stock" name="stock" placeholder="0" min="0">
                </div>
            </div>
            
            <div class="form-group">
                <label for="product-tags">العلامات (مفصولة بفواصل)</label>
                <input type="text" id="product-tags" name="tags" placeholder="علامة1, علامة2, علامة3">
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="product-status">حالة المنتج</label>
                    <select id="product-status" name="status">
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="draft">مسودة</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="product-featured">منتج مميز</label>
                    <select id="product-featured" name="featured">
                        <option value="no">لا</option>
                        <option value="yes">نعم</option>
                    </select>
                </div>
            </div>
            
            <button type="submit" class="submit-btn">💾 حفظ المنتج</button>
        </form>
    </div>
    
    <script>
        document.getElementById('product-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // جمع بيانات النموذج
            const formData = new FormData(this);
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            console.log('بيانات المنتج:', data);
            
            // إظهار رسالة نجاح
            alert('تم حفظ المنتج بنجاح! ✅\n\nبيانات المنتج:\n' + JSON.stringify(data, null, 2));
            
            // إعادة تعيين النموذج للاختبار التالي
            setTimeout(() => {
                this.reset();
            }, 1000);
        });
    </script>
</body>
</html>
