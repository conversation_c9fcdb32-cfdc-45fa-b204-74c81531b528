// Smart Form Filler - Popup Script

class SmartFormPopup {
    constructor() {
        this.currentData = null;
        this.currentPattern = null;
        this.teachingMode = false;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadSavedData();
        this.updateUI();
        this.setupSmartFeatures();
    }
    
    setupEventListeners() {
        console.log('Setting up event listeners...');

        // URL input
        const creationUrlInput = document.getElementById('creation-url');
        const analyzeUrlBtn = document.getElementById('analyze-url');
        const removeUrlBtn = document.getElementById('remove-url');

        if (creationUrlInput && analyzeUrlBtn && removeUrlBtn) {
            analyzeUrlBtn.addEventListener('click', () => this.analyzeUrl());
            removeUrlBtn.addEventListener('click', () => this.removeUrl());
            creationUrlInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.analyzeUrl();
                }
            });
        }

        // Field mapping
        const autoMapBtn = document.getElementById('auto-map-fields');
        if (autoMapBtn) {
            autoMapBtn.addEventListener('click', () => this.autoMapFields());
        }

        // Row selection
        const selectAllBtn = document.getElementById('select-all-rows');
        const deselectAllBtn = document.getElementById('deselect-all-rows');
        const startFillingBtn = document.getElementById('start-filling');

        if (selectAllBtn && deselectAllBtn && startFillingBtn) {
            selectAllBtn.addEventListener('click', () => this.selectAllRows());
            deselectAllBtn.addEventListener('click', () => this.deselectAllRows());
            startFillingBtn.addEventListener('click', () => this.startFilling());
        }

        // File upload
        const fileInput = document.getElementById('file-input');
        const uploadArea = document.getElementById('upload-area');
        const removeFileBtn = document.getElementById('remove-file');

        if (!fileInput || !uploadArea || !removeFileBtn) {
            console.error('Required elements not found:', {
                fileInput: !!fileInput,
                uploadArea: !!uploadArea,
                removeFileBtn: !!removeFileBtn
            });
            return;
        }

        fileInput.addEventListener('change', (e) => {
            console.log('File input changed:', e.target.files);
            this.handleFileSelect(e);
        });

        uploadArea.addEventListener('click', () => {
            console.log('Upload area clicked');
            fileInput.click();
        });

        uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        uploadArea.addEventListener('drop', (e) => this.handleFileDrop(e));
        removeFileBtn.addEventListener('click', () => this.removeFile());
        
        // Teaching mode toggle
        const teachingModeToggle = document.getElementById('teaching-mode');
        teachingModeToggle.addEventListener('change', (e) => this.toggleTeachingMode(e.target.checked));
        
        // Pattern management
        const savePatternBtn = document.getElementById('save-pattern');
        const clearPatternBtn = document.getElementById('clear-pattern');
        
        savePatternBtn.addEventListener('click', () => this.saveCurrentPattern());
        clearPatternBtn.addEventListener('click', () => this.clearCurrentPattern());
        
        // Auto fill
        const startAutofillBtn = document.getElementById('start-autofill');
        startAutofillBtn.addEventListener('click', () => this.startAutoFill());
        
        // Data row selector
        const dataRowSelect = document.getElementById('data-row');
        dataRowSelect.addEventListener('change', (e) => this.updateSelectedRow(e.target.value));
    }
    
    async loadSavedData() {
        try {
            // Load uploaded data and URL info
            const result = await chrome.storage.local.get([
                'uploadedData',
                'currentPattern',
                'savedPatterns',
                'targetUrl',
                'urlAnalyzed',
                'pageFields',
                'fieldMappings'
            ]);

            if (result.uploadedData) {
                this.currentData = result.uploadedData;
                this.displayDataPreview();
                this.populateDataRowSelector();
            }

            if (result.targetUrl) {
                document.getElementById('creation-url').value = result.targetUrl;
                if (result.urlAnalyzed) {
                    this.displayUrlInfo(result.targetUrl, 'صفحة محفوظة');
                    this.showSection('page-fields-section');
                }
            }

            if (result.pageFields) {
                this.pageFields = result.pageFields;
                this.displayPageFields(result.pageFields);
            }

            if (result.fieldMappings) {
                this.fieldMappings = result.fieldMappings;
                this.updateMappingDisplay();
                if (Object.keys(result.fieldMappings).length > 0) {
                    this.showSection('mapping-section');
                    if (this.currentData) {
                        this.showSection('row-selection-section');
                        this.displayRowSelection();
                    }
                }
            }

            if (result.currentPattern) {
                this.currentPattern = result.currentPattern;
                this.updatePatternInfo();
            }

            if (result.savedPatterns) {
                this.displaySavedPatterns(result.savedPatterns);
            }

        } catch (error) {
            console.error('Error loading saved data:', error);
            this.updateStatus('خطأ في تحميل البيانات المحفوظة', 'error');
        }
    }
    
    handleFileSelect(event) {
        console.log('handleFileSelect called');
        const file = event.target.files[0];
        console.log('Selected file:', file);

        if (file) {
            console.log('File details:', {
                name: file.name,
                size: file.size,
                type: file.type,
                lastModified: file.lastModified
            });
            this.processFile(file);
        } else {
            console.log('No file selected');
            this.updateStatus('لم يتم اختيار ملف', 'error');
        }
    }
    
    handleDragOver(event) {
        event.preventDefault();
        event.currentTarget.classList.add('dragover');
    }
    
    handleFileDrop(event) {
        event.preventDefault();
        event.currentTarget.classList.remove('dragover');
        
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }
    
    async processFile(file) {
        try {
            console.log('Processing file:', file.name);
            this.updateStatus('جاري معالجة الملف...', 'info');

            // Validate file type
            const validExtensions = ['.csv', '.json'];
            const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

            if (!validExtensions.includes(fileExtension)) {
                throw new Error(`نوع ملف غير مدعوم. الأنواع المدعومة: ${validExtensions.join(', ')}`);
            }

            console.log('Reading file as text...');
            const text = await this.readFileAsText(file);
            console.log('File text length:', text.length);
            console.log('First 100 characters:', text.substring(0, 100));

            let parsedData;

            if (fileExtension === '.csv') {
                console.log('Parsing as CSV...');
                parsedData = SmartFormUtils.parseCSV(text);
            } else if (fileExtension === '.json') {
                console.log('Parsing as JSON...');
                parsedData = SmartFormUtils.parseJSON(text);
            }

            console.log('Parsed data:', parsedData);

            if (!parsedData || !parsedData.data || parsedData.data.length === 0) {
                throw new Error('الملف فارغ أو تنسيقه غير صحيح');
            }

            if (!parsedData.headers || parsedData.headers.length === 0) {
                throw new Error('لم يتم العثور على رؤوس الأعمدة');
            }

            this.currentData = {
                filename: file.name,
                size: file.size,
                headers: parsedData.headers,
                data: parsedData.data,
                uploadDate: new Date().toISOString()
            };

            console.log('Current data set:', this.currentData);

            // Save to storage
            console.log('Saving to storage...');
            await chrome.storage.local.set({ uploadedData: this.currentData });
            console.log('Saved to storage successfully');

            this.displayFileInfo(file);
            this.displayDataPreview();
            this.populateDataRowSelector();
            this.updateStatus('تم رفع الملف بنجاح', 'success');

            // إظهار قسم الربط إذا كانت الصفحة محللة
            const result = await chrome.storage.local.get(['urlAnalyzed']);
            if (result.urlAnalyzed && this.pageFields) {
                this.showSection('mapping-section');
            }

            // فتح العرض المقسم في تبويب جديد (اختياري)
            // this.openSplitView();

        } catch (error) {
            console.error('Error processing file:', error);
            this.updateStatus(`خطأ في معالجة الملف: ${error.message}`, 'error');
        }
    }
    
    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            console.log('Creating FileReader...');
            const reader = new FileReader();

            reader.onload = (event) => {
                console.log('File read successfully');
                resolve(event.target.result);
            };

            reader.onerror = (event) => {
                console.error('File read error:', event);
                reject(new Error('خطأ في قراءة الملف'));
            };

            reader.onabort = () => {
                console.error('File read aborted');
                reject(new Error('تم إلغاء قراءة الملف'));
            };

            console.log('Starting to read file...');
            reader.readAsText(file, 'UTF-8');
        });
    }
    
    displayFileInfo(file) {
        const fileInfo = document.getElementById('file-info');
        const fileName = document.getElementById('file-name');
        const fileSize = document.getElementById('file-size');
        const uploadArea = document.getElementById('upload-area');
        
        fileName.textContent = file.name;
        fileSize.textContent = SmartFormUtils.formatFileSize(file.size);
        
        uploadArea.style.display = 'none';
        fileInfo.style.display = 'flex';
    }
    
    displayDataPreview() {
        if (!this.currentData) return;
        
        const previewSection = document.getElementById('preview-section');
        const tableHead = document.getElementById('table-head');
        const tableBody = document.getElementById('table-body');
        const rowsCount = document.getElementById('rows-count');
        const columnsCount = document.getElementById('columns-count');
        
        // Clear existing content
        tableHead.innerHTML = '';
        tableBody.innerHTML = '';
        
        // Create header row
        const headerRow = document.createElement('tr');
        this.currentData.headers.forEach(header => {
            const th = document.createElement('th');
            th.textContent = header;
            headerRow.appendChild(th);
        });
        tableHead.appendChild(headerRow);
        
        // Create data rows (show first 5 rows)
        const maxRows = Math.min(5, this.currentData.data.length);
        for (let i = 0; i < maxRows; i++) {
            const row = document.createElement('tr');
            this.currentData.headers.forEach(header => {
                const td = document.createElement('td');
                td.textContent = this.currentData.data[i][header] || '';
                row.appendChild(td);
            });
            tableBody.appendChild(row);
        }
        
        // Update stats
        rowsCount.textContent = this.currentData.data.length;
        columnsCount.textContent = this.currentData.headers.length;
        
        previewSection.style.display = 'block';
    }
    
    populateDataRowSelector() {
        if (!this.currentData) return;
        
        const dataRowSelect = document.getElementById('data-row');
        dataRowSelect.innerHTML = '<option value="">-- اختر صف --</option>';
        
        this.currentData.data.forEach((row, index) => {
            const option = document.createElement('option');
            option.value = index;
            
            // Create a preview of the row data
            const preview = this.currentData.headers.slice(0, 2)
                .map(header => row[header] || '')
                .join(' - ');
            
            option.textContent = `صف ${index + 1}: ${preview}`;
            dataRowSelect.appendChild(option);
        });
    }
    
    removeFile() {
        this.currentData = null;
        
        // Clear storage
        chrome.storage.local.remove(['uploadedData']);
        
        // Reset UI
        const uploadArea = document.getElementById('upload-area');
        const fileInfo = document.getElementById('file-info');
        const previewSection = document.getElementById('preview-section');
        const fileInput = document.getElementById('file-input');
        
        uploadArea.style.display = 'block';
        fileInfo.style.display = 'none';
        previewSection.style.display = 'none';
        fileInput.value = '';
        
        this.updateStatus('تم حذف الملف', 'info');
    }

    async openSplitView() {
        try {
            console.log('🚀 فتح العرض المقسم...');

            // إنشاء URL للعرض المقسم
            const splitViewUrl = chrome.runtime.getURL('split-view.html');

            // فتح تبويب جديد
            const tab = await chrome.tabs.create({
                url: splitViewUrl,
                active: true
            });

            // حفظ البيانات للعرض المقسم
            await chrome.storage.local.set({
                splitViewTabId: tab.id,
                currentData: {
                    data: this.currentData.data,
                    columns: this.currentData.headers
                }
            });

            console.log('✅ تم فتح العرض المقسم بنجاح');
            this.updateStatus('تم فتح العرض المقسم في تبويب جديد', 'success');

        } catch (error) {
            console.error('❌ خطأ في فتح العرض المقسم:', error);
            this.updateStatus('خطأ في فتح العرض المقسم', 'error');
        }
    }
    
    async toggleTeachingMode(enabled) {
        this.teachingMode = enabled;
        
        try {
            // Send message to content script
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            await chrome.tabs.sendMessage(tab.id, {
                action: 'toggleTeachingMode',
                enabled: enabled
            });
            
            this.updatePatternSection();
            this.updateStatus(enabled ? 'تم تفعيل وضع التعليم' : 'تم إلغاء وضع التعليم', 'info');
            
        } catch (error) {
            console.error('Error toggling teaching mode:', error);
            this.updateStatus('خطأ في تغيير وضع التعليم', 'error');
        }
    }
    
    updatePatternSection() {
        const currentPatternDiv = document.getElementById('current-pattern');
        const autofillSection = document.getElementById('autofill-section');
        
        if (this.teachingMode) {
            currentPatternDiv.style.display = 'block';
            autofillSection.style.display = 'none';
        } else {
            currentPatternDiv.style.display = 'none';
            if (this.currentData && this.currentPattern) {
                autofillSection.style.display = 'block';
            }
        }
    }
    
    updatePatternInfo() {
        if (!this.currentPattern) return;
        
        const patternName = document.getElementById('pattern-name');
        const patternUrl = document.getElementById('pattern-url');
        const patternFields = document.getElementById('pattern-fields');
        
        patternName.textContent = this.currentPattern.name || 'نمط جديد';
        patternUrl.textContent = this.currentPattern.url || '';
        patternFields.textContent = `${Object.keys(this.currentPattern.fieldMappings || {}).length} حقل مربوط`;
    }
    
    async saveCurrentPattern() {
        if (!this.currentPattern) {
            this.updateStatus('لا يوجد نمط للحفظ', 'error');
            return;
        }
        
        try {
            const patternName = prompt('أدخل اسم النمط:');
            if (!patternName) return;
            
            this.currentPattern.name = patternName;
            this.currentPattern.id = SmartFormUtils.generateId();
            this.currentPattern.saveDate = new Date().toISOString();
            
            // Get existing patterns
            const result = await chrome.storage.local.get(['savedPatterns']);
            const savedPatterns = result.savedPatterns || [];
            
            // Add new pattern
            savedPatterns.push(this.currentPattern);
            
            // Save to storage
            await chrome.storage.local.set({ savedPatterns });
            
            this.displaySavedPatterns(savedPatterns);
            this.updateStatus('تم حفظ النمط بنجاح', 'success');
            
        } catch (error) {
            console.error('Error saving pattern:', error);
            this.updateStatus('خطأ في حفظ النمط', 'error');
        }
    }
    
    async clearCurrentPattern() {
        this.currentPattern = null;
        await chrome.storage.local.remove(['currentPattern']);
        
        // Send message to content script
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            await chrome.tabs.sendMessage(tab.id, {
                action: 'clearPattern'
            });
        } catch (error) {
            console.error('Error clearing pattern:', error);
        }
        
        this.updatePatternInfo();
        this.updateStatus('تم مسح النمط', 'info');
    }
    
    displaySavedPatterns(patterns) {
        const patternsList = document.getElementById('patterns-list');
        
        if (!patterns || patterns.length === 0) {
            patternsList.innerHTML = '<div class="no-patterns">لا توجد أنماط محفوظة</div>';
            return;
        }
        
        patternsList.innerHTML = '';
        patterns.forEach(pattern => {
            const patternDiv = document.createElement('div');
            patternDiv.className = 'pattern-item';
            patternDiv.innerHTML = `
                <div class="pattern-info">
                    <div class="pattern-name">${pattern.name}</div>
                    <div class="pattern-url">${pattern.url}</div>
                    <div class="pattern-fields">${Object.keys(pattern.fieldMappings || {}).length} حقل</div>
                </div>
                <div class="pattern-actions">
                    <button class="btn btn-primary btn-small" onclick="popup.loadPattern('${pattern.id}')">تحميل</button>
                    <button class="btn btn-secondary btn-small" onclick="popup.deletePattern('${pattern.id}')">حذف</button>
                </div>
            `;
            patternsList.appendChild(patternDiv);
        });
    }
    
    async startAutoFill() {
        if (!this.currentData || !this.currentPattern) {
            this.updateStatus('تأكد من رفع البيانات وتحديد النمط', 'error');
            return;
        }
        
        const selectedRowIndex = document.getElementById('data-row').value;
        const waitTime = parseInt(document.getElementById('wait-time').value) || 3;
        
        if (selectedRowIndex === '') {
            this.updateStatus('اختر صف البيانات أولاً', 'error');
            return;
        }
        
        try {
            const selectedRow = this.currentData.data[selectedRowIndex];
            
            // Send message to content script
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            await chrome.tabs.sendMessage(tab.id, {
                action: 'startAutoFill',
                data: selectedRow,
                pattern: this.currentPattern,
                waitTime: waitTime * 1000
            });
            
            this.showProgress();
            this.updateStatus('جاري التعبئة التلقائية...', 'info');
            
        } catch (error) {
            console.error('Error starting auto fill:', error);
            this.updateStatus('خطأ في بدء التعبئة التلقائية', 'error');
        }
    }
    
    showProgress() {
        const progressDiv = document.getElementById('autofill-progress');
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        
        progressDiv.style.display = 'block';
        progressFill.style.width = '0%';
        progressText.textContent = 'جاري التعبئة...';
        
        // Simulate progress
        let progress = 0;
        const interval = setInterval(() => {
            progress += 10;
            progressFill.style.width = progress + '%';
            
            if (progress >= 100) {
                clearInterval(interval);
                progressText.textContent = 'تم الانتهاء';
                setTimeout(() => {
                    progressDiv.style.display = 'none';
                }, 2000);
            }
        }, 200);
    }
    
    updateSelectedRow(rowIndex) {
        // Update UI based on selected row
        if (rowIndex !== '' && this.currentData) {
            const selectedRow = this.currentData.data[rowIndex];
            console.log('Selected row:', selectedRow);
        }
    }
    
    updateStatus(message, type = 'info') {
        const statusElement = document.getElementById('status');
        statusElement.textContent = message;
        statusElement.className = `status ${type}`;
        
        // Reset status after 3 seconds
        setTimeout(() => {
            statusElement.textContent = 'جاهز';
            statusElement.className = 'status';
        }, 3000);
    }
    
    updateUI() {
        // Update UI based on current state
        this.updatePatternSection();
    }

    // ===== الميزات الذكية الجديدة =====

    setupSmartFeatures() {
        this.setupAutoDetection();
        this.setupRecording();
        this.isRecording = false;
        this.recordedSteps = [];
    }

    setupAutoDetection() {
        const autoDetectionToggle = document.getElementById('auto-detection-mode');
        if (autoDetectionToggle) {
            autoDetectionToggle.addEventListener('change', (e) => {
                this.toggleAutoDetection(e.target.checked);
            });
        }
    }

    setupRecording() {
        const startRecordingBtn = document.getElementById('start-recording');
        const stopRecordingBtn = document.getElementById('stop-recording');
        const playRecordingBtn = document.getElementById('play-recording');
        const saveRecordingBtn = document.getElementById('save-recording');
        const exportRecordingBtn = document.getElementById('export-recording');
        const clearRecordingBtn = document.getElementById('clear-recording');
        const importRecordingBtn = document.getElementById('import-recording');
        const exportAllBtn = document.getElementById('export-all-recordings');

        if (startRecordingBtn) {
            startRecordingBtn.addEventListener('click', () => {
                this.startRecording();
            });
        }

        if (stopRecordingBtn) {
            stopRecordingBtn.addEventListener('click', () => {
                this.stopRecording();
            });
        }

        if (playRecordingBtn) {
            playRecordingBtn.addEventListener('click', () => {
                this.playRecording();
            });
        }

        if (saveRecordingBtn) {
            saveRecordingBtn.addEventListener('click', () => {
                this.saveCurrentRecording();
            });
        }

        if (exportRecordingBtn) {
            exportRecordingBtn.addEventListener('click', () => {
                this.exportCurrentRecording();
            });
        }

        if (clearRecordingBtn) {
            clearRecordingBtn.addEventListener('click', () => {
                this.clearCurrentRecording();
            });
        }

        if (importRecordingBtn) {
            importRecordingBtn.addEventListener('click', () => {
                this.importRecording();
            });
        }

        if (exportAllBtn) {
            exportAllBtn.addEventListener('click', () => {
                this.exportAllRecordings();
            });
        }

        // تحميل التسجيلات المحفوظة
        this.loadSavedRecordings();
    }

    toggleAutoDetection(enabled) {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.tabs.sendMessage(tabs[0].id, {
                action: 'toggleAutoDetection',
                enabled: enabled
            });
        });

        if (enabled) {
            this.updateStatus('تم تفعيل وضع التعرف التلقائي', 'success');
            this.showDetectionResults();
        } else {
            this.updateStatus('تم إيقاف وضع التعرف التلقائي', 'info');
            this.hideDetectionResults();
        }
    }

    showDetectionResults() {
        const resultsSection = document.getElementById('detection-results');
        if (resultsSection) {
            resultsSection.style.display = 'block';

            // محاكاة نتائج الكشف
            setTimeout(() => {
                this.updateDetectionCounts({
                    createButtons: 2,
                    dropdowns: 3,
                    radioButtons: 4,
                    modals: 1
                });
            }, 1000);
        }
    }

    hideDetectionResults() {
        const resultsSection = document.getElementById('detection-results');
        if (resultsSection) {
            resultsSection.style.display = 'none';
        }
    }

    updateDetectionCounts(counts) {
        const elements = {
            'create-buttons-count': counts.createButtons || 0,
            'dropdowns-count': counts.dropdowns || 0,
            'radio-buttons-count': counts.radioButtons || 0,
            'modals-count': counts.modals || 0
        };

        Object.entries(elements).forEach(([id, count]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = count;
            }
        });
    }

    startRecording() {
        this.isRecording = true;
        this.recordedSteps = [];

        // تحديث واجهة المستخدم
        this.updateRecordingUI('recording');

        // إرسال رسالة لبدء التسجيل مع تفعيل التعلم الذكي
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.tabs.sendMessage(tabs[0].id, {
                action: 'startRecording'
            });

            // تفعيل التعلم الذكي تلقائياً أثناء التسجيل
            chrome.tabs.sendMessage(tabs[0].id, {
                action: 'toggleTeachingMode',
                enabled: true
            });
        });

        this.updateStatus('بدء تسجيل الخطوات مع التعلم الذكي...', 'success');
    }

    stopRecording() {
        this.isRecording = false;

        // تحديث واجهة المستخدم
        this.updateRecordingUI('ready');

        // إرسال رسالة لإيقاف التسجيل
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.tabs.sendMessage(tabs[0].id, {
                action: 'stopRecording'
            });

            // إيقاف وضع التعليم إذا لم يكن مفعلاً يدوياً
            const teachingToggle = document.getElementById('teaching-mode');
            if (!teachingToggle || !teachingToggle.checked) {
                chrome.tabs.sendMessage(tabs[0].id, {
                    action: 'toggleTeachingMode',
                    enabled: false
                });
            }
        });

        this.updateStatus('تم إيقاف التسجيل', 'info');
        this.showRecordedSteps();
    }

    playRecording() {
        if (!this.currentData || !this.currentData.data || this.currentData.data.length === 0) {
            this.updateStatus('لا توجد بيانات للتشغيل', 'warning');
            return;
        }

        // تحديث واجهة المستخدم
        this.updateRecordingUI('playing');

        // إرسال رسالة لتشغيل التسجيل
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.tabs.sendMessage(tabs[0].id, {
                action: 'playRecording',
                data: this.currentData.data
            });
        });

        this.updateStatus('بدء تشغيل الخطوات المسجلة...', 'info');

        // إعادة تعيين الحالة بعد فترة
        setTimeout(() => {
            this.updateRecordingUI('ready');
        }, 5000);
    }

    updateRecordingUI(state) {
        const statusIndicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('status-text');
        const startBtn = document.getElementById('start-recording');
        const stopBtn = document.getElementById('stop-recording');
        const playBtn = document.getElementById('play-recording');

        if (statusIndicator) {
            statusIndicator.className = `status-indicator ${state}`;
        }

        switch (state) {
            case 'recording':
                if (statusIndicator) statusIndicator.textContent = '🔴';
                if (statusText) statusText.textContent = 'جاري التسجيل...';
                if (startBtn) startBtn.disabled = true;
                if (stopBtn) stopBtn.disabled = false;
                if (playBtn) playBtn.disabled = true;
                break;

            case 'ready':
                if (statusIndicator) statusIndicator.textContent = '🟢';
                if (statusText) statusText.textContent = 'جاهز للتسجيل';
                if (startBtn) startBtn.disabled = false;
                if (stopBtn) stopBtn.disabled = true;
                if (playBtn) playBtn.disabled = this.recordedSteps.length === 0;
                break;

            case 'playing':
                if (statusIndicator) statusIndicator.textContent = '▶️';
                if (statusText) statusText.textContent = 'جاري التشغيل...';
                if (startBtn) startBtn.disabled = true;
                if (stopBtn) stopBtn.disabled = true;
                if (playBtn) playBtn.disabled = true;
                break;
        }
    }

    showRecordedSteps() {
        const stepsSection = document.getElementById('recorded-steps');
        const stepsList = document.getElementById('steps-list');
        const stepsCount = document.getElementById('steps-count');

        if (stepsSection) {
            stepsSection.style.display = 'block';
        }

        if (stepsCount) {
            stepsCount.textContent = this.recordedSteps.length;
        }

        if (stepsList) {
            stepsList.innerHTML = '';

            this.recordedSteps.forEach((step, index) => {
                const stepElement = document.createElement('div');
                stepElement.className = 'step-item';
                stepElement.innerHTML = `
                    <div class="step-number">${index + 1}</div>
                    <div class="step-type ${step.type}">${step.type}</div>
                    <div class="step-description">${this.getStepDescription(step)}</div>
                `;
                stepsList.appendChild(stepElement);
            });
        }
    }

    getStepDescription(step) {
        switch (step.type) {
            case 'click':
                if (step.isCreateButton) {
                    return `نقر على زر الإنشاء: ${step.buttonText || step.text}`;
                }
                return `نقر على: ${step.text || step.elementType}`;

            case 'input':
                return `إدخال في حقل: ${step.placeholder || 'حقل نص'}`;

            case 'change':
                if (step.elementType === 'select') {
                    return `اختيار من قائمة: ${step.selectedText || step.selectedValue}`;
                }
                return `تغيير قيمة: ${step.value}`;

            default:
                return `عملية: ${step.type}`;
        }
    }

    updateStepsCount(count) {
        const stepsCount = document.getElementById('steps-count');
        if (stepsCount) {
            stepsCount.textContent = count;

            // إضافة تأثير بصري للعداد
            stepsCount.style.background = '#28a745';
            stepsCount.style.color = 'white';
            stepsCount.style.transform = 'scale(1.2)';

            setTimeout(() => {
                stepsCount.style.background = '';
                stepsCount.style.color = '';
                stepsCount.style.transform = '';
            }, 300);
        }

        // إظهار قسم الخطوات المسجلة إذا كان مخفياً
        const stepsSection = document.getElementById('recorded-steps');
        if (stepsSection && count > 0) {
            stepsSection.style.display = 'block';
        }
    }

    // ===== إدارة التسجيلات المحفوظة =====

    saveCurrentRecording() {
        if (!this.recordedSteps || this.recordedSteps.length === 0) {
            this.updateStatus('لا توجد خطوات للحفظ', 'warning');
            return;
        }

        const name = prompt('أدخل اسم التسجيل:', this.generateRecordingName());
        if (!name) return;

        const recordingData = {
            name: name,
            steps: this.recordedSteps,
            timestamp: Date.now(),
            totalSteps: this.recordedSteps.length,
            fieldMappings: this.currentPattern?.fieldMappings || {},
            url: window.location.href
        };

        this.saveRecordingToStorage(recordingData);
        this.updateStatus(`تم حفظ التسجيل: ${name}`, 'success');
        this.loadSavedRecordings();
    }

    async saveRecordingToStorage(recordingData) {
        try {
            const result = await chrome.storage.local.get(['savedRecordings']);
            const savedRecordings = result.savedRecordings || [];

            savedRecordings.unshift(recordingData);

            // الاحتفاظ بآخر 20 تسجيل
            if (savedRecordings.length > 20) {
                savedRecordings.splice(20);
            }

            await chrome.storage.local.set({ savedRecordings });
        } catch (error) {
            console.error('خطأ في حفظ التسجيل:', error);
            this.updateStatus('خطأ في حفظ التسجيل', 'error');
        }
    }

    exportCurrentRecording() {
        if (!this.recordedSteps || this.recordedSteps.length === 0) {
            this.updateStatus('لا توجد خطوات للتصدير', 'warning');
            return;
        }

        const recordingData = {
            name: this.generateRecordingName(),
            steps: this.recordedSteps,
            timestamp: Date.now(),
            totalSteps: this.recordedSteps.length,
            fieldMappings: this.currentPattern?.fieldMappings || {},
            url: window.location.href,
            version: '1.0'
        };

        this.downloadJSON(recordingData, `recording_${Date.now()}.json`);
        this.updateStatus('تم تصدير التسجيل', 'success');
    }

    clearCurrentRecording() {
        if (confirm('هل أنت متأكد من مسح التسجيل الحالي؟')) {
            this.recordedSteps = [];
            this.updateStepsCount(0);

            const stepsSection = document.getElementById('recorded-steps');
            if (stepsSection) {
                stepsSection.style.display = 'none';
            }

            this.updateStatus('تم مسح التسجيل', 'info');
        }
    }

    importRecording() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const recordingData = JSON.parse(e.target.result);
                        this.loadRecording(recordingData);
                        this.updateStatus(`تم استيراد التسجيل: ${recordingData.name}`, 'success');
                    } catch (error) {
                        this.updateStatus('خطأ في قراءة ملف التسجيل', 'error');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    async exportAllRecordings() {
        try {
            const result = await chrome.storage.local.get(['savedRecordings']);
            const savedRecordings = result.savedRecordings || [];

            if (savedRecordings.length === 0) {
                this.updateStatus('لا توجد تسجيلات للتصدير', 'warning');
                return;
            }

            const exportData = {
                recordings: savedRecordings,
                exportDate: new Date().toISOString(),
                version: '1.0'
            };

            this.downloadJSON(exportData, `all_recordings_${Date.now()}.json`);
            this.updateStatus(`تم تصدير ${savedRecordings.length} تسجيل`, 'success');
        } catch (error) {
            this.updateStatus('خطأ في تصدير التسجيلات', 'error');
        }
    }

    async loadSavedRecordings() {
        try {
            const result = await chrome.storage.local.get(['savedRecordings']);
            const savedRecordings = result.savedRecordings || [];

            this.displaySavedRecordings(savedRecordings);
        } catch (error) {
            console.error('خطأ في تحميل التسجيلات:', error);
        }
    }

    displaySavedRecordings(recordings) {
        const recordingsList = document.getElementById('recordings-list');
        if (!recordingsList) return;

        if (recordings.length === 0) {
            recordingsList.innerHTML = `
                <div class="no-recordings">
                    لا توجد تسجيلات محفوظة
                </div>
            `;
            return;
        }

        recordingsList.innerHTML = recordings.map((recording, index) => `
            <div class="recording-item">
                <div class="recording-header">
                    <div class="recording-name">${recording.name}</div>
                    <div class="recording-date">${new Date(recording.timestamp).toLocaleString('ar-SA')}</div>
                </div>
                <div class="recording-info">
                    <span>📊 ${recording.totalSteps} خطوة</span>
                    <span>🔗 ${Object.keys(recording.fieldMappings || {}).length} ربط</span>
                    <span class="recording-status saved">محفوظ</span>
                </div>
                <div class="recording-actions-item">
                    <button class="btn btn-play" onclick="popup.playRecordingByIndex(${index})">
                        ▶️ تشغيل
                    </button>
                    <button class="btn btn-edit" onclick="popup.loadRecordingByIndex(${index})">
                        ✏️ تحميل
                    </button>
                    <button class="btn btn-export" onclick="popup.exportRecordingByIndex(${index})">
                        📤 تصدير
                    </button>
                    <button class="btn btn-delete" onclick="popup.deleteRecordingByIndex(${index})">
                        🗑️ حذف
                    </button>
                </div>
            </div>
        `).join('');
    }

    generateRecordingName() {
        const now = new Date();
        const date = now.toLocaleDateString('ar-SA');
        const time = now.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
        return `تسجيل ${date} - ${time}`;
    }

    downloadJSON(data, filename) {
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
    }

    async playRecordingByIndex(index) {
        try {
            const result = await chrome.storage.local.get(['savedRecordings']);
            const savedRecordings = result.savedRecordings || [];

            if (savedRecordings[index]) {
                this.loadRecording(savedRecordings[index]);
                this.playRecording();
            }
        } catch (error) {
            this.updateStatus('خطأ في تشغيل التسجيل', 'error');
        }
    }

    async loadRecordingByIndex(index) {
        try {
            const result = await chrome.storage.local.get(['savedRecordings']);
            const savedRecordings = result.savedRecordings || [];

            if (savedRecordings[index]) {
                this.loadRecording(savedRecordings[index]);
                this.updateStatus(`تم تحميل التسجيل: ${savedRecordings[index].name}`, 'success');
            }
        } catch (error) {
            this.updateStatus('خطأ في تحميل التسجيل', 'error');
        }
    }

    async exportRecordingByIndex(index) {
        try {
            const result = await chrome.storage.local.get(['savedRecordings']);
            const savedRecordings = result.savedRecordings || [];

            if (savedRecordings[index]) {
                const recording = savedRecordings[index];
                this.downloadJSON(recording, `${recording.name.replace(/\s+/g, '_')}.json`);
                this.updateStatus(`تم تصدير: ${recording.name}`, 'success');
            }
        } catch (error) {
            this.updateStatus('خطأ في تصدير التسجيل', 'error');
        }
    }

    async deleteRecordingByIndex(index) {
        try {
            const result = await chrome.storage.local.get(['savedRecordings']);
            const savedRecordings = result.savedRecordings || [];

            if (savedRecordings[index]) {
                const recordingName = savedRecordings[index].name;

                if (confirm(`هل أنت متأكد من حذف التسجيل: ${recordingName}؟`)) {
                    savedRecordings.splice(index, 1);
                    await chrome.storage.local.set({ savedRecordings });
                    this.loadSavedRecordings();
                    this.updateStatus(`تم حذف: ${recordingName}`, 'info');
                }
            }
        } catch (error) {
            this.updateStatus('خطأ في حذف التسجيل', 'error');
        }
    }

    loadRecording(recordingData) {
        this.recordedSteps = recordingData.steps || [];
        this.currentPattern = {
            fieldMappings: recordingData.fieldMappings || {},
            url: recordingData.url
        };

        this.updateStepsCount(this.recordedSteps.length);
        this.showRecordedSteps();

        // تحديث زر التشغيل
        const playBtn = document.getElementById('play-recording');
        if (playBtn) {
            playBtn.disabled = false;
        }
    }

    // ===== وظائف النظام الجديد =====

    async analyzeUrl() {
        const urlInput = document.getElementById('creation-url');
        const url = urlInput.value.trim();

        if (!url) {
            this.updateStatus('يرجى إدخال رابط صحيح', 'error');
            return;
        }

        try {
            this.updateStatus('جاري تحليل الصفحة...', 'info');

            // حفظ الرابط في التخزين المؤقت
            await chrome.storage.local.set({
                targetUrl: url,
                urlAnalyzed: true
            });

            // فتح الرابط في تبويب جديد وتحليله
            const tab = await chrome.tabs.create({ url: url, active: false });

            // انتظار تحميل الصفحة
            await this.waitForTabLoad(tab.id);

            // تحليل حقول الصفحة
            const pageInfo = await chrome.tabs.sendMessage(tab.id, {
                action: 'analyzePageFields'
            });

            if (pageInfo && pageInfo.fields) {
                this.displayUrlInfo(url, pageInfo.title);
                this.displayPageFields(pageInfo.fields);
                this.updateStatus('تم تحليل الصفحة بنجاح', 'success');

                // إظهار الأقسام ذات الصلة
                this.showSection('page-fields-section');
                if (this.currentData) {
                    this.showSection('mapping-section');
                }
            } else {
                throw new Error('لم يتم العثور على حقول في الصفحة');
            }

        } catch (error) {
            console.error('Error analyzing URL:', error);
            this.updateStatus(`خطأ في تحليل الصفحة: ${error.message}`, 'error');
        }
    }

    waitForTabLoad(tabId) {
        return new Promise((resolve) => {
            const listener = (updatedTabId, changeInfo) => {
                if (updatedTabId === tabId && changeInfo.status === 'complete') {
                    chrome.tabs.onUpdated.removeListener(listener);
                    // انتظار إضافي للتأكد من تحميل المحتوى
                    setTimeout(resolve, 2000);
                }
            };
            chrome.tabs.onUpdated.addListener(listener);
        });
    }

    displayUrlInfo(url, title) {
        const urlInfo = document.getElementById('url-info');
        const urlTitle = document.getElementById('url-title');
        const urlAddress = document.getElementById('url-address');

        urlTitle.textContent = title || 'صفحة الإنشاء';
        urlAddress.textContent = url;

        urlInfo.style.display = 'flex';
    }

    displayPageFields(fields) {
        const fieldsContainer = document.getElementById('fields-container');
        const availableFieldsCount = document.getElementById('available-fields-count');

        if (!fields || fields.length === 0) {
            fieldsContainer.innerHTML = '<div class="no-fields">لم يتم العثور على حقول</div>';
            return;
        }

        fieldsContainer.innerHTML = '';
        availableFieldsCount.textContent = fields.length;

        fields.forEach((field, index) => {
            const fieldItem = document.createElement('div');
            fieldItem.className = 'field-item';
            fieldItem.innerHTML = `
                <div class="field-info">
                    <div class="field-name">${field.name || field.placeholder || `حقل ${index + 1}`}</div>
                    <div class="field-type">${this.getFieldTypeLabel(field.type)}</div>
                </div>
                <div class="field-actions">
                    <button class="btn btn-primary btn-small" onclick="popup.mapField('${field.selector}', '${field.type}')">
                        ربط
                    </button>
                </div>
            `;
            fieldsContainer.appendChild(fieldItem);
        });

        // حفظ الحقول للاستخدام لاحقاً
        this.pageFields = fields;
    }

    getFieldTypeLabel(type) {
        const typeLabels = {
            'text': 'نص',
            'email': 'بريد إلكتروني',
            'number': 'رقم',
            'tel': 'هاتف',
            'url': 'رابط',
            'password': 'كلمة مرور',
            'textarea': 'نص طويل',
            'select': 'قائمة منسدلة',
            'radio': 'اختيار واحد',
            'checkbox': 'مربع اختيار',
            'date': 'تاريخ',
            'time': 'وقت',
            'datetime-local': 'تاريخ ووقت'
        };
        return typeLabels[type] || type;
    }

    removeUrl() {
        document.getElementById('creation-url').value = '';
        document.getElementById('url-info').style.display = 'none';

        this.hideSection('page-fields-section');
        this.hideSection('mapping-section');
        this.hideSection('row-selection-section');

        chrome.storage.local.remove(['targetUrl', 'urlAnalyzed']);
        this.updateStatus('تم حذف الرابط', 'info');
    }

    showSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = 'block';
        }
    }

    hideSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = 'none';
        }
    }

    mapField(fieldSelector, fieldType) {
        if (!this.currentData || !this.currentData.headers) {
            this.updateStatus('يرجى رفع ملف البيانات أولاً', 'warning');
            return;
        }

        // إنشاء نافذة اختيار العمود
        this.showColumnSelectionModal(fieldSelector, fieldType);
    }

    showColumnSelectionModal(fieldSelector, fieldType) {
        // إنشاء نافذة منبثقة لاختيار العمود
        const modal = document.createElement('div');
        modal.className = 'column-selection-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>اختيار العمود للربط</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <p>اختر العمود المناسب لهذا الحقل:</p>
                    <select id="column-selector" class="column-selector">
                        <option value="">-- اختر عمود --</option>
                        ${this.currentData.headers.map(header =>
                            `<option value="${header}">${header}</option>`
                        ).join('')}
                    </select>
                    ${fieldType === 'select' ? `
                        <div class="custom-value-section">
                            <label>
                                <input type="checkbox" id="use-custom-value">
                                استخدام قيمة مخصصة
                            </label>
                            <input type="text" id="custom-value" placeholder="أدخل القيمة المخصصة" disabled>
                        </div>
                    ` : ''}
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" id="confirm-mapping">تأكيد الربط</button>
                    <button class="btn btn-secondary" id="cancel-mapping">إلغاء</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // إضافة معالجات الأحداث
        modal.querySelector('.modal-close').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.querySelector('#cancel-mapping').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.querySelector('#confirm-mapping').addEventListener('click', () => {
            const selectedColumn = modal.querySelector('#column-selector').value;
            const useCustomValue = modal.querySelector('#use-custom-value')?.checked;
            const customValue = modal.querySelector('#custom-value')?.value;

            if (!selectedColumn && !useCustomValue) {
                this.updateStatus('يرجى اختيار عمود أو قيمة مخصصة', 'warning');
                return;
            }

            this.addFieldMapping(fieldSelector, fieldType, selectedColumn, customValue);
            document.body.removeChild(modal);
        });

        // معالج القيمة المخصصة
        const useCustomCheckbox = modal.querySelector('#use-custom-value');
        const customValueInput = modal.querySelector('#custom-value');
        const columnSelector = modal.querySelector('#column-selector');

        if (useCustomCheckbox && customValueInput) {
            useCustomCheckbox.addEventListener('change', (e) => {
                customValueInput.disabled = !e.target.checked;
                columnSelector.disabled = e.target.checked;
                if (e.target.checked) {
                    columnSelector.value = '';
                    customValueInput.focus();
                }
            });
        }
    }

    addFieldMapping(fieldSelector, fieldType, column, customValue) {
        if (!this.fieldMappings) {
            this.fieldMappings = {};
        }

        this.fieldMappings[fieldSelector] = {
            type: fieldType,
            column: column,
            customValue: customValue
        };

        this.updateMappingDisplay();
        this.updateStatus('تم ربط الحقل بنجاح', 'success');

        // حفظ الربط في التخزين المؤقت
        chrome.storage.local.set({
            fieldMappings: this.fieldMappings,
            pageFields: this.pageFields
        });

        // إظهار قسم تحديد الصفوف إذا كان هناك ربط واحد على الأقل
        if (Object.keys(this.fieldMappings).length > 0 && this.currentData) {
            this.showSection('row-selection-section');
            this.displayRowSelection();
        }
    }

    updateMappingDisplay() {
        const mappingList = document.getElementById('mapping-list');
        const mappedFieldsCount = document.getElementById('mapped-fields-count');

        if (!this.fieldMappings || Object.keys(this.fieldMappings).length === 0) {
            mappingList.innerHTML = '<div class="no-mappings">لا توجد حقول مربوطة</div>';
            mappedFieldsCount.textContent = '0';
            return;
        }

        mappingList.innerHTML = '';
        mappedFieldsCount.textContent = Object.keys(this.fieldMappings).length;

        Object.entries(this.fieldMappings).forEach(([selector, mapping]) => {
            const mappingItem = document.createElement('div');
            mappingItem.className = 'mapping-item';
            mappingItem.innerHTML = `
                <div class="mapping-field">
                    <div class="mapping-field-name">${this.getFieldDisplayName(selector)}</div>
                    <div class="mapping-field-type">${this.getFieldTypeLabel(mapping.type)}</div>
                </div>
                <div class="mapping-column">
                    ${mapping.customValue ?
                        `<span class="custom-value">قيمة مخصصة: ${mapping.customValue}</span>` :
                        `<span class="column-name">${mapping.column}</span>`
                    }
                </div>
                <div class="mapping-actions">
                    <button class="btn btn-secondary btn-small" onclick="popup.removeFieldMapping('${selector}')">
                        حذف
                    </button>
                </div>
            `;
            mappingList.appendChild(mappingItem);
        });
    }

    getFieldDisplayName(selector) {
        // محاولة استخراج اسم مفهوم من المحدد
        if (this.pageFields) {
            const field = this.pageFields.find(f => f.selector === selector);
            if (field) {
                return field.name || field.placeholder || field.id || 'حقل غير محدد';
            }
        }
        return selector;
    }

    removeFieldMapping(selector) {
        if (this.fieldMappings && this.fieldMappings[selector]) {
            delete this.fieldMappings[selector];
            this.updateMappingDisplay();
            this.updateStatus('تم حذف ربط الحقل', 'info');

            // إخفاء قسم تحديد الصفوف إذا لم تعد هناك حقول مربوطة
            if (Object.keys(this.fieldMappings).length === 0) {
                this.hideSection('row-selection-section');
            }
        }
    }

    autoMapFields() {
        if (!this.pageFields || !this.currentData || !this.currentData.headers) {
            this.updateStatus('تأكد من تحليل الصفحة ورفع البيانات', 'warning');
            return;
        }

        let mappedCount = 0;
        this.fieldMappings = {};

        // محاولة ربط تلقائي بناءً على أسماء الحقول
        this.pageFields.forEach(field => {
            const fieldName = (field.name || field.placeholder || field.id || '').toLowerCase();

            // البحث عن عمود مطابق
            const matchingColumn = this.currentData.headers.find(header => {
                const headerLower = header.toLowerCase();
                return headerLower.includes(fieldName) || fieldName.includes(headerLower);
            });

            if (matchingColumn) {
                this.fieldMappings[field.selector] = {
                    type: field.type,
                    column: matchingColumn,
                    customValue: null
                };
                mappedCount++;
            }
        });

        this.updateMappingDisplay();
        this.updateStatus(`تم ربط ${mappedCount} حقل تلقائياً`, 'success');

        if (mappedCount > 0) {
            this.showSection('row-selection-section');
            this.displayRowSelection();
        }
    }

    displayRowSelection() {
        if (!this.currentData || !this.currentData.data) {
            return;
        }

        const rowsList = document.getElementById('rows-list');

        rowsList.innerHTML = '';
        this.selectedRows = [];

        this.currentData.data.forEach((row, index) => {
            const rowItem = document.createElement('div');
            rowItem.className = 'row-item';

            // إنشاء معاينة للصف
            const preview = this.currentData.headers.slice(0, 3)
                .map(header => row[header] || '')
                .join(' - ');

            rowItem.innerHTML = `
                <input type="checkbox" class="row-checkbox" data-row-index="${index}">
                <div class="row-preview">${preview}</div>
                <div class="row-index">صف ${index + 1}</div>
            `;

            // إضافة معالج تغيير الاختيار
            const checkbox = rowItem.querySelector('.row-checkbox');
            checkbox.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.selectedRows.push(index);
                    rowItem.classList.add('selected');
                } else {
                    this.selectedRows = this.selectedRows.filter(i => i !== index);
                    rowItem.classList.remove('selected');
                }
                this.updateSelectedRowsCount();
            });

            rowsList.appendChild(rowItem);
        });

        this.updateSelectedRowsCount();
    }

    selectAllRows() {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        this.selectedRows = [];

        checkboxes.forEach((checkbox, index) => {
            checkbox.checked = true;
            checkbox.closest('.row-item').classList.add('selected');
            this.selectedRows.push(index);
        });

        this.updateSelectedRowsCount();
    }

    deselectAllRows() {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        this.selectedRows = [];

        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            checkbox.closest('.row-item').classList.remove('selected');
        });

        this.updateSelectedRowsCount();
    }

    updateSelectedRowsCount() {
        const selectedRowsCount = document.getElementById('selected-rows-count');
        const startFillingBtn = document.getElementById('start-filling');

        selectedRowsCount.textContent = this.selectedRows.length;
        startFillingBtn.disabled = this.selectedRows.length === 0;
    }

    async startFilling() {
        if (!this.selectedRows || this.selectedRows.length === 0) {
            this.updateStatus('يرجى تحديد صفوف للتعبئة', 'warning');
            return;
        }

        if (!this.fieldMappings || Object.keys(this.fieldMappings).length === 0) {
            this.updateStatus('يرجى ربط الحقول أولاً', 'warning');
            return;
        }

        const createNewForm = document.getElementById('create-new-form').checked;
        const fillDelay = parseInt(document.getElementById('fill-delay').value) * 1000;

        try {
            this.updateStatus('بدء عملية التعبئة...', 'info');

            // الحصول على الرابط المحفوظ
            const result = await chrome.storage.local.get(['targetUrl']);
            const targetUrl = result.targetUrl;

            if (!targetUrl) {
                throw new Error('لم يتم العثور على رابط الصفحة المستهدفة');
            }

            // بدء التعبئة للصفوف المحددة
            for (let i = 0; i < this.selectedRows.length; i++) {
                const rowIndex = this.selectedRows[i];
                const rowData = this.currentData.data[rowIndex];

                this.updateStatus(`تعبئة الصف ${i + 1} من ${this.selectedRows.length}...`, 'info');

                if (createNewForm || i === 0) {
                    // فتح نموذج جديد
                    const tab = await chrome.tabs.create({ url: targetUrl, active: false });
                    await this.waitForTabLoad(tab.id);

                    // تعبئة النموذج
                    await this.fillFormInTab(tab.id, rowData);
                } else {
                    // استخدام النموذج الحالي (إذا كان متاحاً)
                    const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
                    await this.fillFormInTab(currentTab.id, rowData);
                }

                // انتظار قبل المتابعة للصف التالي
                if (i < this.selectedRows.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, fillDelay));
                }
            }

            this.updateStatus('تم الانتهاء من تعبئة جميع الصفوف', 'success');

        } catch (error) {
            console.error('Error during filling:', error);
            this.updateStatus(`خطأ في التعبئة: ${error.message}`, 'error');
        }
    }

    async fillFormInTab(tabId, rowData) {
        return new Promise((resolve, reject) => {
            chrome.tabs.sendMessage(tabId, {
                action: 'fillFormWithMapping',
                fieldMappings: this.fieldMappings,
                rowData: rowData
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else if (response && response.success) {
                    resolve(response);
                } else {
                    reject(new Error(response?.error || 'فشل في تعبئة النموذج'));
                }
            });
        });
    }
}

// Initialize popup when DOM is loaded
let popup;
document.addEventListener('DOMContentLoaded', () => {
    popup = new SmartFormPopup();
});

// Listen for messages from content script
chrome.runtime.onMessage.addListener((message) => {
    if (message.action === 'patternUpdated') {
        popup.currentPattern = message.pattern;
        popup.updatePatternInfo();
        chrome.storage.local.set({ currentPattern: message.pattern });
    } else if (message.action === 'autoFillComplete') {
        popup.updateStatus('تم الانتهاء من التعبئة التلقائية', 'success');
    } else if (message.action === 'autoFillError') {
        popup.updateStatus(`خطأ في التعبئة: ${message.error}`, 'error');
    } else if (message.action === 'stepRecorded') {
        // تحديث عداد الخطوات في الوقت الفعلي
        if (popup && popup.isRecording) {
            popup.recordedSteps.push(message.step);
            popup.updateStepsCount(message.totalSteps);
            popup.updateStatus(`تم تسجيل خطوة ${message.totalSteps}`, 'info');
        }
    } else if (message.action === 'recordingSaved') {
        // تحديث قائمة التسجيلات المحفوظة
        if (popup) {
            popup.loadSavedRecordings();
            popup.updateStatus('تم حفظ التسجيل تلقائياً', 'success');
        }
    }
});
