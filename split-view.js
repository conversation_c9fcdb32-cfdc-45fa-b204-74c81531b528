// Smart Form Filler - Split View System

class SplitViewSystem {
    constructor() {
        this.data = [];
        this.columns = [];
        this.selectedRows = new Set();
        this.currentWebsiteUrl = '';
        
        this.init();
    }
    
    init() {
        console.log('🚀 تهيئة نظام العرض المقسم...');

        this.loadDataFromStorage();
        this.loadSavedUrl();
        this.setupEventListeners();
        this.setupResizeHandle();

        console.log('✅ تم تهيئة نظام العرض المقسم');
    }
    
    async loadDataFromStorage() {
        try {
            console.log('📊 تحميل البيانات من التخزين...');

            const result = await chrome.storage.local.get(['currentData']);
            if (result.currentData) {
                this.data = result.currentData.data || [];
                this.columns = result.currentData.columns || [];

                console.log(`✅ تم تحميل ${this.data.length} صف و ${this.columns.length} عمود`);

                this.displayData();
                this.updateStats();
            } else {
                this.showNoData();
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error);
            this.showError('خطأ في تحميل البيانات');
        }
    }

    async loadSavedUrl() {
        try {
            console.log('🔗 تحميل الرابط المحفوظ...');

            const result = await chrome.storage.local.get(['savedWebsiteUrl']);
            if (result.savedWebsiteUrl) {
                const urlInput = document.getElementById('website-url');
                urlInput.value = result.savedWebsiteUrl;
                this.currentWebsiteUrl = result.savedWebsiteUrl;

                console.log('✅ تم تحميل الرابط المحفوظ:', result.savedWebsiteUrl);
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل الرابط المحفوظ:', error);
        }
    }

    async saveUrl(url) {
        try {
            await chrome.storage.local.set({ savedWebsiteUrl: url });
            console.log('💾 تم حفظ الرابط:', url);
        } catch (error) {
            console.error('❌ خطأ في حفظ الرابط:', error);
        }
    }
    
    displayData() {
        console.log('📋 عرض البيانات في الجدول...');
        
        const container = document.getElementById('data-table-container');
        
        if (!this.data.length || !this.columns.length) {
            this.showNoData();
            return;
        }
        
        // إنشاء الجدول
        const table = document.createElement('table');
        table.className = 'data-table';
        table.id = 'data-table';
        
        // إنشاء رأس الجدول
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        // عمود التحديد
        const selectHeader = document.createElement('th');
        selectHeader.innerHTML = `
            <input type="checkbox" id="select-all" class="row-selector">
            <label for="select-all" style="margin-right: 5px;">تحديد الكل</label>
        `;
        headerRow.appendChild(selectHeader);
        
        // أعمدة البيانات
        this.columns.forEach(column => {
            const th = document.createElement('th');
            th.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span>📊</span>
                    <span>${column}</span>
                    <button class="copy-btn" onclick="splitView.copyColumn('${column}')">نسخ</button>
                </div>
            `;
            headerRow.appendChild(th);
        });
        
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        // إنشاء جسم الجدول
        const tbody = document.createElement('tbody');
        
        this.data.forEach((row, index) => {
            const tr = document.createElement('tr');
            tr.dataset.rowIndex = index;
            
            // خلية التحديد
            const selectCell = document.createElement('td');
            selectCell.innerHTML = `
                <input type="checkbox" class="row-selector" data-row-index="${index}">
                <span style="margin-right: 8px; color: #6c757d; font-size: 12px;">${index + 1}</span>
            `;
            tr.appendChild(selectCell);
            
            // خلايا البيانات
            this.columns.forEach(column => {
                const td = document.createElement('td');
                const value = row[column] || '';
                td.textContent = value;
                td.title = value; // tooltip للقيم الطويلة
                tr.appendChild(td);
            });
            
            tbody.appendChild(tr);
        });
        
        table.appendChild(tbody);
        container.innerHTML = '';
        container.appendChild(table);
        
        // إضافة مستمعي الأحداث
        this.setupTableEventListeners();
        
        console.log('✅ تم عرض البيانات في الجدول');
    }
    
    setupTableEventListeners() {
        // تحديد الكل
        const selectAllCheckbox = document.getElementById('select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }
        
        // تحديد الصفوف الفردية
        document.querySelectorAll('.row-selector[data-row-index]').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const rowIndex = parseInt(e.target.dataset.rowIndex);
                this.toggleRowSelection(rowIndex, e.target.checked);
            });
        });
    }
    
    toggleSelectAll(checked) {
        console.log(`📋 تحديد الكل: ${checked}`);
        
        const rowCheckboxes = document.querySelectorAll('.row-selector[data-row-index]');
        rowCheckboxes.forEach(checkbox => {
            checkbox.checked = checked;
            const rowIndex = parseInt(checkbox.dataset.rowIndex);
            this.toggleRowSelection(rowIndex, checked);
        });
    }
    
    toggleRowSelection(rowIndex, selected) {
        if (selected) {
            this.selectedRows.add(rowIndex);
        } else {
            this.selectedRows.delete(rowIndex);
        }
        
        // تمييز الصف
        const row = document.querySelector(`tr[data-row-index="${rowIndex}"]`);
        if (row) {
            if (selected) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
        }
        
        this.updateStats();
    }
    
    updateStats() {
        document.getElementById('total-rows').textContent = this.data.length;
        document.getElementById('total-columns').textContent = this.columns.length;
        document.getElementById('selected-rows').textContent = this.selectedRows.size;
    }
    
    setupEventListeners() {
        // تحميل الموقع
        document.getElementById('load-website').addEventListener('click', () => {
            this.loadWebsite();
        });
        
        // تحديث الموقع
        document.getElementById('refresh-website').addEventListener('click', () => {
            this.refreshWebsite();
        });
        
        // نسخ البيانات
        document.getElementById('copy-data').addEventListener('click', () => {
            this.copySelectedData();
        });
        
        // Enter في حقل الرابط
        document.getElementById('website-url').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.loadWebsite();
            }
        });
    }
    
    loadWebsite() {
        const urlInput = document.getElementById('website-url');
        const url = urlInput.value.trim();

        if (!url) {
            this.showNotification('يرجى إدخال رابط صحيح', 'warning');
            return;
        }

        // إضافة http:// إذا لم يكن موجود
        let fullUrl = url;
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            fullUrl = 'https://' + url;
        }

        console.log('🌐 تحميل الموقع:', fullUrl);

        const iframe = document.getElementById('website-frame');
        iframe.src = fullUrl;
        this.currentWebsiteUrl = fullUrl;

        // حفظ الرابط للاستخدام المستقبلي
        this.saveUrl(fullUrl);

        this.showNotification('جاري تحميل الموقع...', 'info');
    }
    
    refreshWebsite() {
        // استخدام الرابط المحفوظ أو الرابط الحالي
        const urlToRefresh = this.currentWebsiteUrl || document.getElementById('website-url').value.trim();

        if (urlToRefresh) {
            console.log('🔄 تحديث الموقع:', urlToRefresh);

            // التأكد من أن الرابط صحيح
            let fullUrl = urlToRefresh;
            if (!urlToRefresh.startsWith('http://') && !urlToRefresh.startsWith('https://')) {
                fullUrl = 'https://' + urlToRefresh;
            }

            const iframe = document.getElementById('website-frame');
            iframe.src = fullUrl;
            this.currentWebsiteUrl = fullUrl;

            // تحديث حقل الإدخال بالرابط الصحيح
            document.getElementById('website-url').value = fullUrl;

            // حفظ الرابط المحدث
            this.saveUrl(fullUrl);

            this.showNotification('جاري تحديث الموقع...', 'info');
        } else {
            this.showNotification('لا يوجد رابط للتحديث', 'warning');
        }
    }
    
    copySelectedData() {
        if (this.selectedRows.size === 0) {
            this.showNotification('يرجى تحديد صف واحد على الأقل', 'warning');
            return;
        }
        
        console.log(`📋 نسخ ${this.selectedRows.size} صف`);
        
        // إنشاء البيانات المحددة
        const selectedData = [];
        this.selectedRows.forEach(rowIndex => {
            if (this.data[rowIndex]) {
                selectedData.push(this.data[rowIndex]);
            }
        });
        
        // تحويل إلى CSV
        const csvContent = this.convertToCSV(selectedData);
        
        // نسخ إلى الحافظة
        navigator.clipboard.writeText(csvContent).then(() => {
            this.showNotification(`تم نسخ ${selectedData.length} صف إلى الحافظة`, 'success');
        }).catch(error => {
            console.error('خطأ في النسخ:', error);
            this.showNotification('خطأ في نسخ البيانات', 'error');
        });
    }
    
    copyColumn(columnName) {
        console.log('📊 نسخ العمود:', columnName);
        
        const columnData = this.data.map(row => row[columnName] || '').join('\n');
        
        navigator.clipboard.writeText(columnData).then(() => {
            this.showNotification(`تم نسخ عمود "${columnName}"`, 'success');
        }).catch(error => {
            console.error('خطأ في نسخ العمود:', error);
            this.showNotification('خطأ في نسخ العمود', 'error');
        });
    }
    
    convertToCSV(data) {
        if (!data.length) return '';
        
        // رؤوس الأعمدة
        const headers = this.columns.join(',');
        
        // صفوف البيانات
        const rows = data.map(row => {
            return this.columns.map(column => {
                const value = row[column] || '';
                // إضافة علامات اقتباس إذا كانت القيمة تحتوي على فاصلة
                return value.includes(',') ? `"${value}"` : value;
            }).join(',');
        });
        
        return [headers, ...rows].join('\n');
    }
    
    setupResizeHandle() {
        const resizeHandle = document.getElementById('resize-handle');
        const dataPanel = document.querySelector('.data-panel');
        const websitePanel = document.querySelector('.website-panel');
        
        let isResizing = false;
        
        resizeHandle.addEventListener('mousedown', (e) => {
            isResizing = true;
            document.body.style.cursor = 'col-resize';
            e.preventDefault();
        });
        
        document.addEventListener('mousemove', (e) => {
            if (!isResizing) return;
            
            const containerWidth = window.innerWidth;
            const newDataWidth = (e.clientX / containerWidth) * 100;
            
            // حدود الحد الأدنى والأقصى
            if (newDataWidth >= 20 && newDataWidth <= 80) {
                dataPanel.style.width = newDataWidth + '%';
                websitePanel.style.width = (100 - newDataWidth) + '%';
            }
        });
        
        document.addEventListener('mouseup', () => {
            isResizing = false;
            document.body.style.cursor = 'default';
        });
    }
    
    showNoData() {
        const container = document.getElementById('data-table-container');
        container.innerHTML = `
            <div class="no-data">
                📋 لا توجد بيانات محملة<br>
                <small>يرجى رفع ملف البيانات من الإضافة الرئيسية</small>
            </div>
        `;
    }
    
    showError(message) {
        const container = document.getElementById('data-table-container');
        container.innerHTML = `
            <div class="no-data" style="color: #dc3545;">
                ❌ ${message}
            </div>
        `;
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// تهيئة النظام
const splitView = new SplitViewSystem();

// جعل المتغير متاح عالمياً للاستخدام في HTML
window.splitView = splitView;
