// Smart Form Filler - Split View System

class SplitViewSystem {
    constructor() {
        this.data = [];
        this.columns = [];
        this.selectedRows = new Set();
        this.currentWebsiteUrl = '';
        this.fieldMappings = {};
        this.isLearningMode = false;
        this.savedSteps = [];

        this.init();
    }
    
    init() {
        console.log('🚀 تهيئة نظام العرض المقسم...');

        this.loadDataFromStorage();
        this.loadSavedUrl();
        this.loadSettings();
        this.setupEventListeners();
        this.setupResizeHandle();

        console.log('✅ تم تهيئة نظام العرض المقسم');
    }
    
    async loadDataFromStorage() {
        try {
            console.log('📊 تحميل البيانات من التخزين...');

            const result = await chrome.storage.local.get(['currentData']);
            if (result.currentData) {
                this.data = result.currentData.data || [];
                this.columns = result.currentData.columns || [];

                console.log(`✅ تم تحميل ${this.data.length} صف و ${this.columns.length} عمود`);

                this.displayData();
                this.updateStats();
            } else {
                this.showNoData();
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error);
            this.showError('خطأ في تحميل البيانات');
        }
    }

    async loadSavedUrl() {
        try {
            console.log('🔗 تحميل الرابط المحفوظ...');

            const result = await chrome.storage.local.get(['savedWebsiteUrl']);
            if (result.savedWebsiteUrl) {
                const urlInput = document.getElementById('website-url');
                urlInput.value = result.savedWebsiteUrl;
                this.currentWebsiteUrl = result.savedWebsiteUrl;

                console.log('✅ تم تحميل الرابط المحفوظ:', result.savedWebsiteUrl);
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل الرابط المحفوظ:', error);
        }
    }

    async saveUrl(url) {
        try {
            await chrome.storage.local.set({ savedWebsiteUrl: url });
            console.log('💾 تم حفظ الرابط:', url);
        } catch (error) {
            console.error('❌ خطأ في حفظ الرابط:', error);
        }
    }
    
    displayData() {
        console.log('📋 عرض البيانات في الجدول...');
        
        const container = document.getElementById('data-table-container');
        
        if (!this.data.length || !this.columns.length) {
            this.showNoData();
            return;
        }
        
        // إنشاء الجدول
        const table = document.createElement('table');
        table.className = 'data-table';
        table.id = 'data-table';
        
        // إنشاء رأس الجدول
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        // عمود التحديد
        const selectHeader = document.createElement('th');
        selectHeader.innerHTML = `
            <input type="checkbox" id="select-all" class="row-selector">
            <label for="select-all" style="margin-right: 5px;">تحديد الكل</label>
        `;
        headerRow.appendChild(selectHeader);
        
        // أعمدة البيانات
        this.columns.forEach(column => {
            const th = document.createElement('th');
            th.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span>📊</span>
                    <span>${column}</span>
                    <button class="copy-btn" onclick="splitView.copyColumn('${column}')">نسخ</button>
                </div>
            `;
            headerRow.appendChild(th);
        });
        
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        // إنشاء جسم الجدول
        const tbody = document.createElement('tbody');
        
        this.data.forEach((row, index) => {
            const tr = document.createElement('tr');
            tr.dataset.rowIndex = index;
            
            // خلية التحديد
            const selectCell = document.createElement('td');
            selectCell.innerHTML = `
                <input type="checkbox" class="row-selector" data-row-index="${index}">
                <span style="margin-right: 8px; color: #6c757d; font-size: 12px;">${index + 1}</span>
            `;
            tr.appendChild(selectCell);
            
            // خلايا البيانات
            this.columns.forEach(column => {
                const td = document.createElement('td');
                const value = row[column] || '';
                td.textContent = value;
                td.title = value; // tooltip للقيم الطويلة
                tr.appendChild(td);
            });
            
            tbody.appendChild(tr);
        });
        
        table.appendChild(tbody);
        container.innerHTML = '';
        container.appendChild(table);
        
        // إضافة مستمعي الأحداث
        this.setupTableEventListeners();
        
        console.log('✅ تم عرض البيانات في الجدول');
    }
    
    setupTableEventListeners() {
        // تحديد الكل
        const selectAllCheckbox = document.getElementById('select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }
        
        // تحديد الصفوف الفردية
        document.querySelectorAll('.row-selector[data-row-index]').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const rowIndex = parseInt(e.target.dataset.rowIndex);
                this.toggleRowSelection(rowIndex, e.target.checked);
            });
        });
    }
    
    toggleSelectAll(checked) {
        console.log(`📋 تحديد الكل: ${checked}`);
        
        const rowCheckboxes = document.querySelectorAll('.row-selector[data-row-index]');
        rowCheckboxes.forEach(checkbox => {
            checkbox.checked = checked;
            const rowIndex = parseInt(checkbox.dataset.rowIndex);
            this.toggleRowSelection(rowIndex, checked);
        });
    }
    
    toggleRowSelection(rowIndex, selected) {
        if (selected) {
            this.selectedRows.add(rowIndex);
        } else {
            this.selectedRows.delete(rowIndex);
        }
        
        // تمييز الصف
        const row = document.querySelector(`tr[data-row-index="${rowIndex}"]`);
        if (row) {
            if (selected) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
        }
        
        this.updateStats();
    }
    
    updateStats() {
        document.getElementById('total-rows').textContent = this.data.length;
        document.getElementById('total-columns').textContent = this.columns.length;
        document.getElementById('selected-rows').textContent = this.selectedRows.size;

        // تحديث حالة زر التعبئة
        const fillButton = document.getElementById('start-filling');
        const hasSelectedRows = this.selectedRows.size > 0;
        const hasFieldMappings = Object.keys(this.fieldMappings).length > 0;

        fillButton.disabled = !hasSelectedRows || !hasFieldMappings;

        if (hasSelectedRows && hasFieldMappings) {
            fillButton.textContent = `🚀 تعبئة (${this.selectedRows.size} صف)`;
        } else {
            fillButton.textContent = '🚀 تعبئة';
        }
    }
    
    setupEventListeners() {
        // تحميل الموقع
        document.getElementById('load-website').addEventListener('click', () => {
            this.loadWebsite();
        });

        // تحديث الموقع
        document.getElementById('refresh-website').addEventListener('click', () => {
            this.refreshWebsite();
        });

        // بدء التعبئة
        document.getElementById('start-filling').addEventListener('click', () => {
            this.startFillingProcess();
        });

        // نسخ البيانات
        document.getElementById('copy-data').addEventListener('click', () => {
            this.copySelectedData();
        });

        // Enter في حقل الرابط
        document.getElementById('website-url').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.loadWebsite();
            }
        });
    }
    
    loadWebsite() {
        const urlInput = document.getElementById('website-url');
        const url = urlInput.value.trim();

        if (!url) {
            this.showNotification('يرجى إدخال رابط صحيح', 'warning');
            return;
        }

        // إضافة http:// إذا لم يكن موجود
        let fullUrl = url;
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            fullUrl = 'https://' + url;
        }

        console.log('🌐 تحميل الموقع:', fullUrl);

        const iframe = document.getElementById('website-frame');
        iframe.src = fullUrl;
        this.currentWebsiteUrl = fullUrl;

        // حفظ الرابط للاستخدام المستقبلي
        this.saveUrl(fullUrl);

        this.showNotification('جاري تحميل الموقع...', 'info');
    }
    
    refreshWebsite() {
        // استخدام الرابط المحفوظ أو الرابط الحالي
        const urlToRefresh = this.currentWebsiteUrl || document.getElementById('website-url').value.trim();

        if (urlToRefresh) {
            console.log('🔄 تحديث الموقع:', urlToRefresh);

            // التأكد من أن الرابط صحيح
            let fullUrl = urlToRefresh;
            if (!urlToRefresh.startsWith('http://') && !urlToRefresh.startsWith('https://')) {
                fullUrl = 'https://' + urlToRefresh;
            }

            const iframe = document.getElementById('website-frame');
            iframe.src = fullUrl;
            this.currentWebsiteUrl = fullUrl;

            // تحديث حقل الإدخال بالرابط الصحيح
            document.getElementById('website-url').value = fullUrl;

            // حفظ الرابط المحدث
            this.saveUrl(fullUrl);

            this.showNotification('جاري تحديث الموقع...', 'info');
        } else {
            this.showNotification('لا يوجد رابط للتحديث', 'warning');
        }
    }
    
    copySelectedData() {
        if (this.selectedRows.size === 0) {
            this.showNotification('يرجى تحديد صف واحد على الأقل', 'warning');
            return;
        }
        
        console.log(`📋 نسخ ${this.selectedRows.size} صف`);
        
        // إنشاء البيانات المحددة
        const selectedData = [];
        this.selectedRows.forEach(rowIndex => {
            if (this.data[rowIndex]) {
                selectedData.push(this.data[rowIndex]);
            }
        });
        
        // تحويل إلى CSV
        const csvContent = this.convertToCSV(selectedData);
        
        // نسخ إلى الحافظة
        navigator.clipboard.writeText(csvContent).then(() => {
            this.showNotification(`تم نسخ ${selectedData.length} صف إلى الحافظة`, 'success');
        }).catch(error => {
            console.error('خطأ في النسخ:', error);
            this.showNotification('خطأ في نسخ البيانات', 'error');
        });
    }
    
    copyColumn(columnName) {
        console.log('📊 نسخ العمود:', columnName);
        
        const columnData = this.data.map(row => row[columnName] || '').join('\n');
        
        navigator.clipboard.writeText(columnData).then(() => {
            this.showNotification(`تم نسخ عمود "${columnName}"`, 'success');
        }).catch(error => {
            console.error('خطأ في نسخ العمود:', error);
            this.showNotification('خطأ في نسخ العمود', 'error');
        });
    }
    
    convertToCSV(data) {
        if (!data.length) return '';
        
        // رؤوس الأعمدة
        const headers = this.columns.join(',');
        
        // صفوف البيانات
        const rows = data.map(row => {
            return this.columns.map(column => {
                const value = row[column] || '';
                // إضافة علامات اقتباس إذا كانت القيمة تحتوي على فاصلة
                return value.includes(',') ? `"${value}"` : value;
            }).join(',');
        });
        
        return [headers, ...rows].join('\n');
    }
    
    setupResizeHandle() {
        const resizeHandle = document.getElementById('resize-handle');
        const dataPanel = document.querySelector('.data-panel');
        const websitePanel = document.querySelector('.website-panel');
        
        let isResizing = false;
        
        resizeHandle.addEventListener('mousedown', (e) => {
            isResizing = true;
            document.body.style.cursor = 'col-resize';
            e.preventDefault();
        });
        
        document.addEventListener('mousemove', (e) => {
            if (!isResizing) return;
            
            const containerWidth = window.innerWidth;
            const newDataWidth = (e.clientX / containerWidth) * 100;
            
            // حدود الحد الأدنى والأقصى
            if (newDataWidth >= 20 && newDataWidth <= 80) {
                dataPanel.style.width = newDataWidth + '%';
                websitePanel.style.width = (100 - newDataWidth) + '%';
            }
        });
        
        document.addEventListener('mouseup', () => {
            isResizing = false;
            document.body.style.cursor = 'default';
        });
    }
    
    showNoData() {
        const container = document.getElementById('data-table-container');
        container.innerHTML = `
            <div class="no-data">
                📋 لا توجد بيانات محملة<br>
                <small>يرجى رفع ملف البيانات من الإضافة الرئيسية</small>
            </div>
        `;
    }
    
    showError(message) {
        const container = document.getElementById('data-table-container');
        container.innerHTML = `
            <div class="no-data" style="color: #dc3545;">
                ❌ ${message}
            </div>
        `;
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // ===== وظائف التعبئة التلقائية =====

    async startFillingProcess() {
        if (this.selectedRows.size === 0) {
            this.showNotification('يرجى تحديد صف واحد على الأقل', 'warning');
            return;
        }

        if (Object.keys(this.fieldMappings).length === 0) {
            this.showNotification('يرجى تعلم الحقول أولاً', 'warning');
            this.startLearningMode();
            return;
        }

        console.log('🚀 بدء عملية التعبئة...');
        this.showNotification('بدء التعبئة التلقائية...', 'info');

        try {
            // تحويل الصفوف المحددة إلى مصفوفة
            const selectedRowsArray = Array.from(this.selectedRows);

            for (let i = 0; i < selectedRowsArray.length; i++) {
                const rowIndex = selectedRowsArray[i];
                const rowData = this.data[rowIndex];

                this.showNotification(`تعبئة الصف ${i + 1} من ${selectedRowsArray.length}...`, 'info');

                // تعبئة النموذج للصف الحالي
                await this.fillFormWithData(rowData, rowIndex);

                // انتظار بين الصفوف
                if (i < selectedRowsArray.length - 1) {
                    await this.delay(2000);
                }
            }

            this.showNotification('تم الانتهاء من التعبئة بنجاح!', 'success');

        } catch (error) {
            console.error('خطأ في التعبئة:', error);
            this.showNotification('خطأ في عملية التعبئة', 'error');
        }
    }

    async fillFormWithData(rowData, rowIndex) {
        console.log(`📝 تعبئة البيانات للصف ${rowIndex + 1}:`, rowData);

        const iframe = document.getElementById('website-frame');
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

        if (!iframeDoc) {
            throw new Error('لا يمكن الوصول إلى محتوى الصفحة');
        }

        // تطبيق الربط المحفوظ
        for (const [fieldSelector, mapping] of Object.entries(this.fieldMappings)) {
            try {
                const element = iframeDoc.querySelector(fieldSelector);
                if (!element) {
                    console.warn(`لم يتم العثور على العنصر: ${fieldSelector}`);
                    continue;
                }

                let value;
                if (mapping.customValue) {
                    value = mapping.customValue;
                } else if (mapping.column && rowData[mapping.column] !== undefined) {
                    value = rowData[mapping.column];
                } else {
                    console.warn(`لا توجد قيمة للحقل: ${fieldSelector}`);
                    continue;
                }

                // تعبئة الحقل
                await this.fillField(element, value, mapping.type);

                // إضافة تأخير قصير بين الحقول
                await this.delay(300);

            } catch (error) {
                console.error(`خطأ في تعبئة الحقل ${fieldSelector}:`, error);
            }
        }

        // النقر على زر الإرسال إذا كان محفوظاً
        if (this.savedSteps.submitButton) {
            try {
                const submitButton = iframeDoc.querySelector(this.savedSteps.submitButton);
                if (submitButton) {
                    await this.delay(500);
                    submitButton.click();
                    console.log('✅ تم النقر على زر الإرسال');
                }
            } catch (error) {
                console.error('خطأ في النقر على زر الإرسال:', error);
            }
        }
    }

    async fillField(element, value, fieldType) {
        // إضافة تمييز بصري
        element.classList.add('field-filling');

        try {
            element.focus();

            switch (fieldType) {
                case 'select':
                    await this.fillSelectField(element, value);
                    break;
                case 'radio':
                    await this.fillRadioField(element, value);
                    break;
                case 'checkbox':
                    await this.fillCheckboxField(element, value);
                    break;
                default:
                    await this.fillTextField(element, value);
                    break;
            }

            // إضافة تمييز للحقل المكتمل
            element.classList.remove('field-filling');
            element.classList.add('field-mapped');

        } catch (error) {
            element.classList.remove('field-filling');
            throw error;
        }
    }

    async fillTextField(element, value) {
        element.value = '';

        // محاكاة الكتابة التدريجية
        for (let i = 0; i < value.length; i++) {
            element.value += value[i];
            element.dispatchEvent(new Event('input', { bubbles: true }));
            await this.delay(50);
        }

        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
    }

    async fillSelectField(element, value) {
        const options = element.querySelectorAll('option');
        let optionFound = false;

        for (const option of options) {
            if (option.value === value ||
                option.textContent.trim() === value ||
                option.textContent.trim().includes(value)) {

                element.value = option.value;
                optionFound = true;
                break;
            }
        }

        if (!optionFound) {
            console.warn(`لم يتم العثور على الخيار: ${value}`);
        }

        element.dispatchEvent(new Event('change', { bubbles: true }));
    }

    async fillRadioField(element, value) {
        const radioButtons = element.form?.querySelectorAll(`input[name="${element.name}"]`) ||
                           document.querySelectorAll(`input[name="${element.name}"]`);

        for (const radio of radioButtons) {
            if (radio.value === value) {
                radio.checked = true;
                radio.dispatchEvent(new Event('change', { bubbles: true }));
                break;
            }
        }
    }

    async fillCheckboxField(element, value) {
        const shouldCheck = value === true ||
                           value === 'true' ||
                           value === '1' ||
                           value === 'نعم' ||
                           value === 'yes';

        element.checked = shouldCheck;
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // ===== وضع التعلم =====

    startLearningMode() {
        if (this.isLearningMode) {
            this.showNotification('وضع التعلم مفعل بالفعل', 'info');
            return;
        }

        this.isLearningMode = true;
        this.fieldMappings = {};
        this.savedSteps = {};

        console.log('🎓 بدء وضع التعلم...');
        this.showNotification('🎓 وضع التعلم مفعل', 'info');

        // إظهار تعليمات مفصلة
        setTimeout(() => {
            this.showNotification('🖱️ انقر بالزر الأيمن على الحقول لربطها بالأعمدة', 'info');
        }, 1000);

        setTimeout(() => {
            this.showNotification('📝 أو انقر بالزر الأيسر للنافذة التفصيلية', 'info');
        }, 2000);

        const iframe = document.getElementById('website-frame');
        iframe.classList.add('learning-mode-active');

        // إضافة مستمعي الأحداث للإطار
        this.setupIframeEventListeners();
    }

    setupIframeEventListeners() {
        const iframe = document.getElementById('website-frame');

        iframe.addEventListener('load', () => {
            this.addFieldEventListeners();
        });

        // إضافة المستمعين للمحتوى الحالي إذا كان محملاً
        this.addFieldEventListeners();
    }

    addFieldEventListeners() {
        const iframe = document.getElementById('website-frame');
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

        if (!iframeDoc) return;

        // البحث عن جميع الحقول القابلة للتعبئة
        const fieldSelectors = [
            'input[type="text"]',
            'input[type="email"]',
            'input[type="number"]',
            'input[type="tel"]',
            'input[type="url"]',
            'input[type="password"]',
            'textarea',
            'select',
            'input[type="radio"]',
            'input[type="checkbox"]'
        ];

        fieldSelectors.forEach(selector => {
            const elements = iframeDoc.querySelectorAll(selector);
            elements.forEach(element => {
                this.addFieldClickListener(element);
            });
        });

        // البحث عن أزرار الإرسال
        const submitButtons = iframeDoc.querySelectorAll('button[type="submit"], input[type="submit"], button:contains("إرسال"), button:contains("حفظ"), button:contains("إنشاء")');
        submitButtons.forEach(button => {
            this.addSubmitButtonListener(button);
        });
    }

    addFieldClickListener(element) {
        // النقر بالزر الأيمن - قائمة السياق
        element.addEventListener('contextmenu', (e) => {
            if (!this.isLearningMode) return;

            e.preventDefault();
            e.stopPropagation();

            this.showContextMenu(e, element);
        });

        // النقر بالزر الأيسر - النافذة التقليدية
        element.addEventListener('click', (e) => {
            if (!this.isLearningMode) return;

            e.preventDefault();
            e.stopPropagation();

            this.showFieldMappingDialog(element);
        });

        element.addEventListener('mouseenter', () => {
            if (this.isLearningMode) {
                element.classList.add('field-highlight');
            }
        });

        element.addEventListener('mouseleave', () => {
            element.classList.remove('field-highlight');
        });
    }

    addSubmitButtonListener(button) {
        button.addEventListener('click', (e) => {
            if (!this.isLearningMode) return;

            e.preventDefault();
            e.stopPropagation();

            // حفظ زر الإرسال
            this.savedSteps.submitButton = this.generateElementSelector(button);
            this.showNotification('تم حفظ زر الإرسال', 'success');

            // إنهاء وضع التعلم
            this.endLearningMode();
        });
    }

    showContextMenu(event, element) {
        // إزالة أي قائمة سياق موجودة
        this.hideContextMenu();

        const fieldName = this.getFieldName(element);
        const fieldType = element.type || element.tagName.toLowerCase();

        const contextMenu = this.createContextMenu(fieldName, fieldType, element);

        // تحديد موقع القائمة
        const x = event.clientX;
        const y = event.clientY;

        contextMenu.style.left = x + 'px';
        contextMenu.style.top = y + 'px';

        document.body.appendChild(contextMenu);

        // إضافة مستمع لإخفاء القائمة عند النقر خارجها
        setTimeout(() => {
            document.addEventListener('click', this.hideContextMenuHandler.bind(this), { once: true });
        }, 100);
    }

    createContextMenu(fieldName, fieldType, element) {
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.id = 'field-context-menu';

        let menuHTML = `
            <div class="context-menu-header">
                📝 ${fieldName}
            </div>
        `;

        // إضافة خيارات الأعمدة مع معاينة البيانات
        this.columns.forEach(column => {
            const sampleData = this.getSampleDataForColumn(column);
            const displayText = sampleData ? `${column} (مثال: ${sampleData})` : column;

            menuHTML += `
                <div class="context-menu-item" data-column="${column}" title="${displayText}">
                    <span class="column-icon">📊</span>
                    <div style="font-weight: bold;">${column}</div>
                    ${sampleData ? `<div style="font-size: 12px; color: #666; margin-top: 2px;">مثال: ${sampleData}</div>` : ''}
                </div>
            `;
        });

        // إضافة خيار القيمة المخصصة للقوائم المنسدلة
        if (fieldType === 'select') {
            menuHTML += `
                <div class="context-menu-item custom-value" data-custom="true">
                    <span class="custom-icon">✏️</span>
                    قيمة مخصصة...
                </div>
            `;
        }

        menu.innerHTML = menuHTML;

        // إضافة معالجات الأحداث
        menu.querySelectorAll('.context-menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();

                if (item.dataset.custom) {
                    this.handleCustomValueSelection(element, fieldType);
                } else {
                    const column = item.dataset.column;
                    this.handleColumnSelection(element, column, fieldType);
                }

                this.hideContextMenu();
            });
        });

        return menu;
    }

    handleColumnSelection(element, column, fieldType) {
        this.saveFieldMapping(element, column, null, fieldType);

        const fieldName = this.getFieldName(element);
        const sampleData = this.getSampleDataForColumn(column);
        const message = sampleData ?
            `تم ربط "${fieldName}" بالعمود "${column}" (مثال: ${sampleData})` :
            `تم ربط "${fieldName}" بالعمود "${column}"`;

        this.showNotification(message, 'success');

        // تأثير بصري للتأكيد
        this.addSuccessEffect(element);
    }

    handleCustomValueSelection(element, fieldType) {
        const customValue = prompt('أدخل القيمة المخصصة:');
        if (customValue) {
            this.saveFieldMapping(element, null, customValue, fieldType);

            const fieldName = this.getFieldName(element);
            this.showNotification(`تم ربط "${fieldName}" بالقيمة "${customValue}"`, 'success');
        }
    }

    hideContextMenu() {
        const existingMenu = document.getElementById('field-context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }
    }

    hideContextMenuHandler(event) {
        const contextMenu = document.getElementById('field-context-menu');
        if (contextMenu && !contextMenu.contains(event.target)) {
            this.hideContextMenu();
        }
    }

    showFieldMappingDialog(element) {
        const fieldName = this.getFieldName(element);
        const fieldType = element.type || element.tagName.toLowerCase();

        // إنشاء نافذة اختيار العمود
        const dialog = this.createMappingDialog(fieldName, fieldType, element);
        document.body.appendChild(dialog);
    }

    createMappingDialog(fieldName, fieldType, element) {
        const dialog = document.createElement('div');
        dialog.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10000;
            min-width: 300px;
            direction: rtl;
        `;

        dialog.innerHTML = `
            <h3 style="margin: 0 0 15px 0; color: #007bff;">ربط الحقل: ${fieldName}</h3>
            <p style="margin: 0 0 15px 0; color: #666;">نوع الحقل: ${fieldType}</p>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">اختر العمود:</label>
                <select id="column-select" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <option value="">-- اختر عمود --</option>
                    ${this.columns.map(col => `<option value="${col}">${col}</option>`).join('')}
                </select>
            </div>

            ${fieldType === 'select' ? `
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">أو أدخل قيمة ثابتة:</label>
                    <input type="text" id="custom-value" placeholder="قيمة مخصصة" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
            ` : ''}

            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                <button id="confirm-mapping" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">تأكيد</button>
                <button id="cancel-mapping" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">إلغاء</button>
            </div>
        `;

        // إضافة معالجات الأحداث
        dialog.querySelector('#confirm-mapping').addEventListener('click', () => {
            const selectedColumn = dialog.querySelector('#column-select').value;
            const customValue = dialog.querySelector('#custom-value')?.value;

            if (!selectedColumn && !customValue) {
                alert('يرجى اختيار عمود أو إدخال قيمة مخصصة');
                return;
            }

            this.saveFieldMapping(element, selectedColumn, customValue, fieldType);
            document.body.removeChild(dialog);
        });

        dialog.querySelector('#cancel-mapping').addEventListener('click', () => {
            document.body.removeChild(dialog);
        });

        return dialog;
    }

    saveFieldMapping(element, column, customValue, fieldType) {
        const selector = this.generateElementSelector(element);

        this.fieldMappings[selector] = {
            column: column,
            customValue: customValue,
            type: fieldType,
            fieldName: this.getFieldName(element)
        };

        element.classList.add('field-mapped');

        console.log('💾 تم حفظ ربط الحقل:', this.fieldMappings[selector]);
        this.showNotification(`تم ربط الحقل: ${this.getFieldName(element)}`, 'success');

        // تحديث حالة زر التعبئة
        this.updateStats();
    }

    generateElementSelector(element) {
        if (element.id) {
            return `#${element.id}`;
        }

        if (element.name) {
            return `[name="${element.name}"]`;
        }

        // إنشاء محدد CSS فريد
        let selector = element.tagName.toLowerCase();

        if (element.className) {
            selector += '.' + element.className.split(' ').join('.');
        }

        // إضافة فهرس إذا لزم الأمر
        const siblings = Array.from(element.parentNode.children).filter(child =>
            child.tagName === element.tagName && child.className === element.className
        );

        if (siblings.length > 1) {
            const index = siblings.indexOf(element) + 1;
            selector += `:nth-child(${index})`;
        }

        return selector;
    }

    getFieldName(element) {
        return element.placeholder ||
               element.name ||
               element.id ||
               element.getAttribute('aria-label') ||
               'حقل غير محدد';
    }

    getSampleDataForColumn(column) {
        if (this.data.length > 0 && this.data[0][column]) {
            const sampleValue = this.data[0][column].toString();
            // قطع النص إذا كان طويلاً
            return sampleValue.length > 20 ? sampleValue.substring(0, 20) + '...' : sampleValue;
        }
        return null;
    }

    addSuccessEffect(element) {
        // إضافة تأثير نجاح مؤقت
        element.style.transition = 'all 0.3s ease';
        element.style.transform = 'scale(1.05)';
        element.style.boxShadow = '0 0 20px rgba(40, 167, 69, 0.6)';

        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.boxShadow = '';
        }, 300);
    }

    endLearningMode() {
        this.isLearningMode = false;

        const iframe = document.getElementById('website-frame');
        iframe.classList.remove('learning-mode-active');

        const mappedFieldsCount = Object.keys(this.fieldMappings).length;

        console.log('✅ انتهى وضع التعلم');
        this.showNotification(`تم حفظ ${mappedFieldsCount} حقل للتعبئة التلقائية`, 'success');

        // حفظ الإعدادات
        this.saveSettings();
    }

    async saveSettings() {
        try {
            await chrome.storage.local.set({
                fieldMappings: this.fieldMappings,
                savedSteps: this.savedSteps,
                websiteUrl: this.currentWebsiteUrl
            });
            console.log('💾 تم حفظ الإعدادات');
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
        }
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.local.get(['fieldMappings', 'savedSteps']);

            if (result.fieldMappings) {
                this.fieldMappings = result.fieldMappings;
                console.log('📥 تم تحميل ربط الحقول:', this.fieldMappings);
            }

            if (result.savedSteps) {
                this.savedSteps = result.savedSteps;
                console.log('📥 تم تحميل الخطوات المحفوظة:', this.savedSteps);
            }

            this.updateStats();

        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
        }
    }
}

// تهيئة النظام
const splitView = new SplitViewSystem();

// جعل المتغير متاح عالمياً للاستخدام في HTML
window.splitView = splitView;
