<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Form Filler - عرض مقسم</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            overflow: hidden;
            direction: rtl;
        }
        
        .split-container {
            display: flex;
            height: 100vh;
            width: 100vw;
        }
        
        .data-panel {
            width: 40%;
            background: #f8f9fa;
            border-left: 3px solid #007bff;
            overflow-y: auto;
            position: relative;
        }
        
        .website-panel {
            width: 60%;
            background: white;
            position: relative;
        }
        
        .panel-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            font-size: 16px;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .data-content {
            padding: 20px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        
        .data-table th {
            background: #e9ecef;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
        }
        
        .data-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .data-table tr.selected {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        
        .row-selector {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }
        
        .website-frame {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .stats-bar {
            background: white;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 20px;
            align-items: center;
            font-size: 14px;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6c757d;
        }
        
        .stat-value {
            font-weight: bold;
            color: #007bff;
        }
        
        .controls-bar {
            background: white;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .url-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .resize-handle {
            width: 4px;
            background: #007bff;
            cursor: col-resize;
            position: relative;
            transition: background 0.2s;
        }
        
        .resize-handle:hover {
            background: #0056b3;
        }
        
        .resize-handle::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 40px;
            background: rgba(0,123,255,0.1);
            border-radius: 10px;
            opacity: 0;
            transition: opacity 0.2s;
        }
        
        .resize-handle:hover::before {
            opacity: 1;
        }
        
        .no-data {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
            font-style: italic;
        }
        
        .loading {
            text-align: center;
            padding: 40px 20px;
            color: #007bff;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #007bff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .copy-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            margin-right: 5px;
        }
        
        .copy-btn:hover {
            background: #138496;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideDown 0.3s ease-out;
        }
        
        .notification.success {
            background: #28a745;
        }
        
        .notification.info {
            background: #17a2b8;
        }
        
        .notification.warning {
            background: #ffc107;
            color: #212529;
        }
        
        @keyframes slideDown {
            from { transform: translateX(-50%) translateY(-100%); }
            to { transform: translateX(-50%) translateY(0); }
        }

        /* أنماط وضع التعلم */
        .learning-mode-active {
            border: 3px solid #28a745 !important;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.3) !important;
        }

        .field-highlight {
            outline: 2px solid #007bff !important;
            outline-offset: 2px !important;
            background-color: rgba(0, 123, 255, 0.1) !important;
        }

        .field-mapped {
            outline: 2px solid #28a745 !important;
            outline-offset: 2px !important;
            background-color: rgba(40, 167, 69, 0.1) !important;
        }

        .field-filling {
            outline: 2px solid #ffc107 !important;
            outline-offset: 2px !important;
            background-color: rgba(255, 193, 7, 0.1) !important;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        /* قائمة السياق */
        .context-menu {
            position: fixed;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10000;
            min-width: 200px;
            max-width: 300px;
            direction: rtl;
            font-family: inherit;
        }

        .context-menu-header {
            background: #007bff;
            color: white;
            padding: 10px 15px;
            font-weight: bold;
            font-size: 14px;
            border-radius: 6px 6px 0 0;
        }

        .context-menu-item {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
            font-size: 14px;
        }

        .context-menu-item:hover {
            background-color: #f8f9fa;
        }

        .context-menu-item:last-child {
            border-bottom: none;
            border-radius: 0 0 6px 6px;
        }

        .context-menu-item.custom-value {
            background-color: #e3f2fd;
            border-top: 2px solid #2196f3;
        }

        .context-menu-item.custom-value:hover {
            background-color: #bbdefb;
        }

        .column-icon {
            margin-left: 8px;
            color: #007bff;
        }

        .custom-icon {
            margin-left: 8px;
            color: #ff9800;
        }

        /* نافذة التعيين التلقائي */
        .auto-setup-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            direction: rtl;
        }

        .auto-setup-content {
            background: white;
            border-radius: 12px;
            padding: 0;
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }

        .auto-setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .auto-setup-header h2 {
            margin: 0;
            font-size: 20px;
        }

        .auto-setup-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .auto-setup-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .auto-setup-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .field-mapping-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 15px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .field-mapping-item:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }

        .field-info {
            flex: 1;
            margin-left: 15px;
        }

        .field-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .field-type {
            font-size: 12px;
            color: #666;
            background: #dee2e6;
            padding: 2px 8px;
            border-radius: 12px;
            display: inline-block;
        }

        .field-mapping {
            flex: 2;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .column-select {
            flex: 1;
            padding: 8px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .column-select:focus {
            outline: none;
            border-color: #007bff;
        }

        .custom-value-input {
            flex: 1;
            padding: 8px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            display: none;
        }

        .custom-value-input.active {
            display: block;
        }

        .toggle-custom {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
        }

        .toggle-custom.active {
            background: #28a745;
        }

        .auto-setup-footer {
            padding: 20px;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
        }

        .setup-stats {
            color: #666;
            font-size: 14px;
        }

        .setup-actions {
            display: flex;
            gap: 10px;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }
    </style>
</head>
<body>
    <div class="split-container">
        <!-- لوحة البيانات - اليمين -->
        <div class="data-panel">
            <div class="panel-header">
                📊 البيانات المحملة
            </div>
            
            <div class="stats-bar">
                <div class="stat-item">
                    <span>📋</span>
                    <span>الصفوف: <span class="stat-value" id="total-rows">0</span></span>
                </div>
                <div class="stat-item">
                    <span>📊</span>
                    <span>الأعمدة: <span class="stat-value" id="total-columns">0</span></span>
                </div>
                <div class="stat-item">
                    <span>✅</span>
                    <span>المحدد: <span class="stat-value" id="selected-rows">0</span></span>
                </div>
            </div>
            
            <div class="data-content">
                <div id="data-table-container">
                    <div class="loading">
                        جاري تحميل البيانات...
                    </div>
                </div>
            </div>
        </div>
        
        <!-- مقبض تغيير الحجم -->
        <div class="resize-handle" id="resize-handle"></div>
        
        <!-- لوحة الموقع - اليسار -->
        <div class="website-panel">
            <div class="panel-header">
                🌐 صفحة الموقع
            </div>
            
            <div class="controls-bar">
                <input type="url" class="url-input" id="website-url" placeholder="أدخل رابط الموقع..." value="https://example.com">
                <button class="btn btn-primary" id="load-website">تحميل</button>
                <button class="btn btn-secondary" id="refresh-website">تحديث</button>
                <button class="btn btn-warning" id="auto-setup">⚙️ تعيين تلقائي</button>
                <button class="btn btn-success" id="start-filling" disabled>🚀 تعبئة</button>
                <button class="btn btn-success" id="copy-data">نسخ البيانات</button>
            </div>
            
            <iframe class="website-frame" id="website-frame" src="about:blank"></iframe>
        </div>
    </div>
    
    <script src="split-view.js"></script>
</body>
</html>
